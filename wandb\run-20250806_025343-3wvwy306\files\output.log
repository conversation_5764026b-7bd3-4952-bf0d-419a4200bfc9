Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,638.97715
Policy Entropy: 3.95451
Value Function Loss: 0.07533

Mean KL Divergence: 0.00044
SB3 Clip Fraction: 0.01484
Policy Update Magnitude: 0.12199
Value Function Update Magnitude: 0.22649

Collected Steps per Second: 8,946.23234
Overall Steps per Second: 5,064.61806

Timestep Collection Time: 5.59342
Timestep Consumption Time: 4.28689
PPO Batch Consumption Time: 0.92744
Total Iteration Time: 9.88031

Cumulative Model Updates: 63,666
Cumulative Timesteps: 354,406,218

Timesteps Collected: 50,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,470.75379
Policy Entropy: 3.96701
Value Function Loss: 0.06619

Mean KL Divergence: 0.00151
SB3 Clip Fraction: 0.06690
Policy Update Magnitude: 0.27849
Value Function Update Magnitude: 0.48263

Collected Steps per Second: 10,023.10285
Overall Steps per Second: 6,680.54328

Timestep Collection Time: 4.99306
Timestep Consumption Time: 2.49824
PPO Batch Consumption Time: 0.25613
Total Iteration Time: 7.49131

Cumulative Model Updates: 63,672
Cumulative Timesteps: 354,456,264

Timesteps Collected: 50,046
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,679.46634
Policy Entropy: 3.96680
Value Function Loss: 0.06424

Mean KL Divergence: 0.00237
SB3 Clip Fraction: 0.11249
Policy Update Magnitude: 0.39182
Value Function Update Magnitude: 0.66491

Collected Steps per Second: 8,641.51886
Overall Steps per Second: 5,397.44243

Timestep Collection Time: 5.78833
Timestep Consumption Time: 3.47902
PPO Batch Consumption Time: 0.26532
Total Iteration Time: 9.26735

Cumulative Model Updates: 63,681
Cumulative Timesteps: 354,506,284

Timesteps Collected: 50,020
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,137.87895
Policy Entropy: 3.97209
Value Function Loss: 0.06404

Mean KL Divergence: 0.00211
SB3 Clip Fraction: 0.10020
Policy Update Magnitude: 0.35296
Value Function Update Magnitude: 0.63411

Collected Steps per Second: 9,406.30816
Overall Steps per Second: 5,618.65689

Timestep Collection Time: 5.31601
Timestep Consumption Time: 3.58363
PPO Batch Consumption Time: 0.26859
Total Iteration Time: 8.89964

Cumulative Model Updates: 63,690
Cumulative Timesteps: 354,556,288

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,357.12551
Policy Entropy: 3.96501
Value Function Loss: 0.06427

Mean KL Divergence: 0.00187
SB3 Clip Fraction: 0.08727
Policy Update Magnitude: 0.34805
Value Function Update Magnitude: 0.63497

Collected Steps per Second: 7,350.30014
Overall Steps per Second: 4,839.38521

Timestep Collection Time: 6.80353
Timestep Consumption Time: 3.53001
PPO Batch Consumption Time: 0.25820
Total Iteration Time: 10.33354

Cumulative Model Updates: 63,699
Cumulative Timesteps: 354,606,296

Timesteps Collected: 50,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,895.20925
Policy Entropy: 3.96742
Value Function Loss: 0.05783

Mean KL Divergence: 0.00174
SB3 Clip Fraction: 0.08035
Policy Update Magnitude: 0.34268
Value Function Update Magnitude: 0.60250

Collected Steps per Second: 10,153.90541
Overall Steps per Second: 6,100.77077

Timestep Collection Time: 4.92540
Timestep Consumption Time: 3.27226
PPO Batch Consumption Time: 0.25602
Total Iteration Time: 8.19765

Cumulative Model Updates: 63,708
Cumulative Timesteps: 354,656,308

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,017.38706
Policy Entropy: 3.97061
Value Function Loss: 0.05789

Mean KL Divergence: 0.00158
SB3 Clip Fraction: 0.07234
Policy Update Magnitude: 0.33278
Value Function Update Magnitude: 0.58029

Collected Steps per Second: 9,664.36477
Overall Steps per Second: 5,852.53546

Timestep Collection Time: 5.17509
Timestep Consumption Time: 3.37060
PPO Batch Consumption Time: 0.25933
Total Iteration Time: 8.54570

Cumulative Model Updates: 63,717
Cumulative Timesteps: 354,706,322

Timesteps Collected: 50,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 6,915.07265
Policy Entropy: 3.97019
Value Function Loss: 0.05958

Mean KL Divergence: 0.00159
SB3 Clip Fraction: 0.07305
Policy Update Magnitude: 0.33470
Value Function Update Magnitude: 0.55718

Collected Steps per Second: 10,792.88183
Overall Steps per Second: 6,323.97183

Timestep Collection Time: 4.63305
Timestep Consumption Time: 3.27400
PPO Batch Consumption Time: 0.25705
Total Iteration Time: 7.90706

Cumulative Model Updates: 63,726
Cumulative Timesteps: 354,756,326

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 5,098.07868
Policy Entropy: 3.96929
Value Function Loss: 0.06386

Mean KL Divergence: 0.00165
SB3 Clip Fraction: 0.07511
Policy Update Magnitude: 0.33883
Value Function Update Magnitude: 0.58142

Collected Steps per Second: 10,236.60660
Overall Steps per Second: 6,094.44590

Timestep Collection Time: 4.88560
Timestep Consumption Time: 3.32056
PPO Batch Consumption Time: 0.25791
Total Iteration Time: 8.20616

Cumulative Model Updates: 63,735
Cumulative Timesteps: 354,806,338

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


Saving checkpoint 354806338...
Checkpoint 354806338 saved!
