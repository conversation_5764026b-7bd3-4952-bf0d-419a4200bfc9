2025-09-21 20:57:05,651 INFO    MainThread:12704 [wandb_setup.py:_flush():81] Current SDK version is 0.22.0
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_setup.py:_flush():81] Configure stats pid to 12704
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\Downloads\Rlgym\Rlgym\wandb\settings
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_init.py:setup_run_log_directory():686] Logging user logs to C:\Users\<USER>\Downloads\Rlgym\Rlgym\wandb\run-20250921_205705-qfioqv7n\logs\debug.log
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_init.py:setup_run_log_directory():687] Logging internal logs to C:\Users\<USER>\Downloads\Rlgym\Rlgym\wandb\run-20250921_205705-qfioqv7n\logs\debug-internal.log
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_init.py:init():813] calling init triggers
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_init.py:init():818] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 100000000000, 'exp_buffer_size': 300000, 'ts_per_iteration': 100000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (512, 512, 512, 512), 'critic_layer_sizes': (512, 512, 512, 512), 'ppo_epochs': 1, 'ppo_batch_size': 100000, 'ppo_minibatch_size': 50000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-09-21 20:57:05,652 INFO    MainThread:12704 [wandb_init.py:init():861] starting backend
