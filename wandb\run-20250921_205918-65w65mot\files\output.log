Created new wandb run! 65w<PERSON><PERSON>
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 69.36636
Policy Entropy: 4.49940
Value Function Loss: nan

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.17756
Value Function Update Magnitude: 0.16798

Collected Steps per Second: 5,690.69208
Overall Steps per Second: 4,316.42385

Timestep Collection Time: 17.57748
Timestep Consumption Time: 5.59634
PPO Batch Consumption Time: 2.04033
Total Iteration Time: 23.17381

Cumulative Model Updates: 1
Cumulative Timesteps: 100,028

Timesteps Collected: 100,028
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 71.16235
Policy Entropy: 4.49937
Value Function Loss: 238.83360

Mean KL Divergence: 0.00001
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.25409
Value Function Update Magnitude: 0.29278

Collected Steps per Second: 5,067.30966
Overall Steps per Second: 4,389.68398

Timestep Collection Time: 19.73750
Timestep Consumption Time: 3.04683
PPO Batch Consumption Time: 0.39430
Total Iteration Time: 22.78433

Cumulative Model Updates: 3
Cumulative Timesteps: 200,044

Timesteps Collected: 100,016
--------END ITERATION REPORT--------


Saving checkpoint 200044...
Checkpoint 200044 saved!
