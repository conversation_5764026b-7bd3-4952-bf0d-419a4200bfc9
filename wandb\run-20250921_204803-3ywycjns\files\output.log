Created new wandb run! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 57.74891
Policy Entropy: 4.49940
Value Function Loss: nan

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.17757
Value Function Update Magnitude: 0.16742

Collected Steps per Second: 11,347.06962
Overall Steps per Second: 8,163.36683

Timestep Collection Time: 8.81620
Timestep Consumption Time: 3.43831
PPO Batch Consumption Time: 1.39368
Total Iteration Time: 12.25450

Cumulative Model Updates: 1
Cumulative Timesteps: 100,038

Timesteps Collected: 100,038
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 67.28555
Policy Entropy: 4.49936
Value Function Loss: 228.45539

Mean KL Divergence: 0.00002
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.25881
Value Function Update Magnitude: 0.29234

Collected Steps per Second: 11,876.37302
Overall Steps per Second: 9,426.25513

Timestep Collection Time: 8.42210
Timestep Consumption Time: 2.18911
PPO Batch Consumption Time: 0.34957
Total Iteration Time: 10.61121

Cumulative Model Updates: 3
Cumulative Timesteps: 200,062

Timesteps Collected: 100,024
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 37.04296
Policy Entropy: 4.49918
Value Function Loss: 152.69930

Mean KL Divergence: 0.00010
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.30448
Value Function Update Magnitude: 0.39333

Collected Steps per Second: 11,809.42717
Overall Steps per Second: 9,268.89876

Timestep Collection Time: 8.46832
Timestep Consumption Time: 2.32110
PPO Batch Consumption Time: 0.27433
Total Iteration Time: 10.78942

Cumulative Model Updates: 6
Cumulative Timesteps: 300,068

Timesteps Collected: 100,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 75.08778
Policy Entropy: 4.49876
Value Function Loss: 3.05535

Mean KL Divergence: 0.00021
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.22535
Value Function Update Magnitude: 0.36457

Collected Steps per Second: 11,812.48812
Overall Steps per Second: 9,274.72677

Timestep Collection Time: 8.46866
Timestep Consumption Time: 2.31721
PPO Batch Consumption Time: 0.27886
Total Iteration Time: 10.78587

Cumulative Model Updates: 9
Cumulative Timesteps: 400,104

Timesteps Collected: 100,036
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 68.28703
Policy Entropy: 4.49817
Value Function Loss: 2.11627

Mean KL Divergence: 0.00026
SB3 Clip Fraction: 0.00007
Policy Update Magnitude: 0.16566
Value Function Update Magnitude: 0.36260

Collected Steps per Second: 11,750.32734
Overall Steps per Second: 9,163.79412

Timestep Collection Time: 8.51227
Timestep Consumption Time: 2.40264
PPO Batch Consumption Time: 0.28184
Total Iteration Time: 10.91491

Cumulative Model Updates: 12
Cumulative Timesteps: 500,126

Timesteps Collected: 100,022
--------END ITERATION REPORT--------


Saving checkpoint 500126...


LEARNING LOOP ENCOUNTERED AN ERROR
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Rlgym\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\Rlgym\Rlgym\rlgym_ppo\learner.py", line 322, in _learn
    self.save(self.agent.cumulative_timesteps)
  File "C:\Users\<USER>\Downloads\Rlgym\Rlgym\rlgym_ppo\learner.py", line 454, in save
    json.dump(book_keeping_vars, f, indent=4)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type float32 is not JSON serializable

Saving checkpoint 500126...
FAILED TO SAVE ON EXIT
