"""
File: device_manager.py
Author: RLGymPPO DirectML Integration

Description:
    Device management utility for handling DirectML, CUDA, and CPU device detection
    and initialization with proper fallback logic and error handling.
"""

import logging
import platform
import warnings
from typing import Optional, Tuple

import torch


class DeviceManager:
    """
    Manages device detection and initialization for PyTorch with DirectML, CUDA, and CPU fallback.
    
    Supports the following device types:
    - "directml" or "dml": Use DirectML for Windows GPU acceleration
    - "cuda": Use CUDA for NVIDIA GPU acceleration  
    - "cpu": Use CPU for computation
    - "auto": Automatically detect the best available device
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the device manager.

        Args:
            logger: Optional logger instance for device detection messages
        """
        self.logger = logger or logging.getLogger(__name__)
        self._directml_available = None
        self._directml_device = None
        self._initialization_errors = []
        
    def _check_directml_availability(self) -> bool:
        """
        Check if DirectML is available and can be used.
        
        Returns:
            bool: True if DirectML is available and functional, False otherwise
        """
        if self._directml_available is not None:
            return self._directml_available
            
        # DirectML is only available on Windows
        if platform.system() != "Windows":
            self._directml_available = False
            return False
            
        try:
            import torch_directml
            
            # Test if DirectML device can be created
            test_device = torch_directml.device()
            
            # Test basic tensor operations
            test_tensor = torch.tensor([1.0, 2.0]).to(test_device)
            result = test_tensor + test_tensor
            result.cpu()  # Move back to CPU to verify operation worked
            
            self._directml_device = test_device
            self._directml_available = True
            self.logger.info("DirectML is available and functional")
            return True
            
        except ImportError as e:
            error_msg = "torch-directml package not installed. DirectML support disabled."
            self.logger.warning(error_msg)
            self.logger.info("To install DirectML support: pip install torch-directml")
            self._initialization_errors.append(f"ImportError: {e}")
            self._directml_available = False
            return False
        except Exception as e:
            error_msg = f"DirectML initialization failed: {e}. DirectML support disabled."
            self.logger.warning(error_msg)
            self.logger.debug(f"DirectML error details: {type(e).__name__}: {e}")
            self._initialization_errors.append(f"{type(e).__name__}: {e}")
            self._directml_available = False
            return False
    
    def _get_directml_device(self):
        """
        Get the DirectML device instance.
        
        Returns:
            DirectML device instance or None if not available
        """
        if not self._check_directml_availability():
            return None
            
        if self._directml_device is not None:
            return self._directml_device
            
        try:
            import torch_directml
            self._directml_device = torch_directml.device()
            return self._directml_device
        except Exception as e:
            error_msg = f"Failed to create DirectML device: {e}"
            self.logger.error(error_msg)
            self.logger.debug(f"DirectML device creation error details: {type(e).__name__}: {e}")
            self._initialization_errors.append(f"Device creation failed: {type(e).__name__}: {e}")
            return None
    
    def get_device(self, device_preference: str = "auto") -> Tuple[torch.device, str]:
        """
        Get the best available device based on preference and availability.
        
        Args:
            device_preference: Device preference string. Options:
                - "directml" or "dml": Force DirectML usage
                - "cuda": Force CUDA usage
                - "cpu": Force CPU usage
                - "auto": Automatically select best available device
                - "gpu": Try DirectML first, then CUDA, then CPU
                
        Returns:
            Tuple of (torch.device, device_type_string)
            
        Raises:
            RuntimeError: If requested device is not available
        """
        device_preference = device_preference.lower().strip()
        
        # Handle DirectML requests
        if device_preference in ["directml", "dml"]:
            if self._check_directml_availability():
                dml_device = self._get_directml_device()
                if dml_device is not None:
                    self.logger.info("Using DirectML device for GPU acceleration")
                    return dml_device, "directml"
            raise RuntimeError("DirectML requested but not available")
        
        # Handle CUDA requests
        elif device_preference == "cuda":
            if torch.cuda.is_available():
                device = torch.device("cuda:0")
                torch.backends.cudnn.benchmark = True
                self.logger.info("Using CUDA device for GPU acceleration")
                return device, "cuda"
            raise RuntimeError("CUDA requested but not available")
        
        # Handle CPU requests
        elif device_preference == "cpu":
            device = torch.device("cpu")
            self.logger.info("Using CPU device")
            return device, "cpu"
        
        # Handle automatic device selection
        elif device_preference in ["auto", "gpu"]:
            # Priority order: DirectML (Windows) -> CUDA -> CPU
            
            # Try DirectML first on Windows
            if self._check_directml_availability():
                dml_device = self._get_directml_device()
                if dml_device is not None:
                    self.logger.info("Auto-selected DirectML device for GPU acceleration")
                    return dml_device, "directml"
            
            # Try CUDA next
            if torch.cuda.is_available():
                device = torch.device("cuda:0")
                torch.backends.cudnn.benchmark = True
                self.logger.info("Auto-selected CUDA device for GPU acceleration")
                return device, "cuda"
            
            # Fallback to CPU
            device = torch.device("cpu")
            if device_preference == "gpu":
                self.logger.warning("No GPU acceleration available, falling back to CPU")
            else:
                self.logger.info("Auto-selected CPU device")
            return device, "cpu"
        
        else:
            # Try to parse as a direct device string (e.g., "cuda:1")
            try:
                device = torch.device(device_preference)
                self.logger.info(f"Using specified device: {device}")
                return device, str(device.type)
            except Exception:
                raise ValueError(f"Unknown device preference: {device_preference}")
    
    def get_device_info(self) -> dict:
        """
        Get information about available devices.

        Returns:
            Dictionary containing device availability information
        """
        info = {
            "platform": platform.system(),
            "directml_available": self._check_directml_availability(),
            "cuda_available": torch.cuda.is_available(),
            "cpu_available": True,
            "initialization_errors": self._initialization_errors.copy()
        }

        if info["cuda_available"]:
            info["cuda_device_count"] = torch.cuda.device_count()
            info["cuda_device_name"] = torch.cuda.get_device_name(0) if torch.cuda.device_count() > 0 else None

        return info

    def get_initialization_errors(self) -> list:
        """
        Get list of initialization errors that occurred during device detection.

        Returns:
            List of error messages
        """
        return self._initialization_errors.copy()
    
    def log_device_info(self):
        """Log information about available devices."""
        info = self.get_device_info()

        self.logger.info("=== Device Information ===")
        self.logger.info(f"Platform: {info['platform']}")
        self.logger.info(f"DirectML available: {info['directml_available']}")
        self.logger.info(f"CUDA available: {info['cuda_available']}")

        if info["cuda_available"]:
            self.logger.info(f"CUDA devices: {info['cuda_device_count']}")
            if info.get("cuda_device_name"):
                self.logger.info(f"CUDA device name: {info['cuda_device_name']}")

        # Log any initialization errors
        if info["initialization_errors"]:
            self.logger.warning("Device initialization errors encountered:")
            for error in info["initialization_errors"]:
                self.logger.warning(f"  - {error}")

        self.logger.info("========================")


# Global device manager instance
_device_manager = None


def get_device_manager() -> DeviceManager:
    """Get the global device manager instance."""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager()
    return _device_manager


def get_optimal_device(device_preference: str = "auto") -> Tuple[torch.device, str]:
    """
    Convenience function to get the optimal device.

    Args:
        device_preference: Device preference string

    Returns:
        Tuple of (torch.device, device_type_string)
    """
    return get_device_manager().get_device(device_preference)


def ensure_tensor_device_compatibility(tensor: torch.Tensor, target_device: torch.device) -> torch.Tensor:
    """
    Ensure tensor is compatible with the target device, handling DirectML specifics.

    Args:
        tensor: Input tensor
        target_device: Target device

    Returns:
        Tensor moved to target device with proper dtype
    """
    # Ensure tensor is float32 for DirectML compatibility
    if tensor.dtype != torch.float32:
        tensor = tensor.float()

    # Move to target device if not already there
    if tensor.device != target_device:
        tensor = tensor.to(target_device)

    return tensor
