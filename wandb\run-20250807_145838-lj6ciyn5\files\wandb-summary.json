{"RW TweakedTouchVelChange": 0.001934651112868284, "RW TouchBallReward": 0.026612016395081477, "RW FaceBallReward": 0.7387705125750343, "PPO Batch Consumption Time": 0.4809734026590983, "Policy Update Magnitude": 0.1309805065393448, "Timestep Collection Time": 7.271070300001156, "Policy Entropy": 4.083923445807563, "RW SaveBoostReward": 0.1252381085573795, "RW VelocityPlayerToBallReward": 0.3397963104519881, "Value Function Update Magnitude": 0.26893141865730286, "_wandb": {"runtime": 27471}, "z_vel": 1.6183165920780986, "Value Function Loss": 0.019914801232516766, "_timestamp": 1754596784.8477037, "Mean KL Divergence": 0.0008572091982286009, "_runtime": 27471, "Collected Steps per Second": 13754.233678635193, "RW VelocityBallToGoalReward": -0.0010846811257239615, "_step": 4065, "x_vel": -12.464843283602821, "Cumulative Timesteps": 76425772, "RW AdjustableBoostReward": 0.00359911215206631, "y_vel": 54.09783605230488, "Total Iteration Time": 13.385905799998, "Overall Steps per Second": 7471.141773611984, "RW AlignBallGoal": 1.059179804521185, "RW EventReward": 0, "Cumulative Model Updates": 6780, "SB3 Clip Fraction": 0.03118555506484376, "Timesteps Collected": 100008, "RW InAirReward": 0.1304895608351332, "Timestep Consumption Time": 6.114835499996843, "Policy Reward": 661.7379715719318}