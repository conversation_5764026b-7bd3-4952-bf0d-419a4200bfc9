Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 91.75502
Policy Entropy: 4.30679
Value Function Loss: 0.00526

Mean KL Divergence: 0.00002
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.04108
Value Function Update Magnitude: 0.11563

Collected Steps per Second: 12,749.70098
Overall Steps per Second: 6,897.92629

Timestep Collection Time: 7.84426
Timestep Consumption Time: 6.65459
PPO Batch Consumption Time: 1.22570
Total Iteration Time: 14.49885

Cumulative Model Updates: 12,867
Cumulative Timesteps: 144,450,574

Timesteps Collected: 100,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 99.08085
Policy Entropy: 4.31395
Value Function Loss: 0.00475

Mean KL Divergence: 0.00016
SB3 Clip Fraction: 0.00338
Policy Update Magnitude: 0.04949
Value Function Update Magnitude: 0.09190

Collected Steps per Second: 11,849.01986
Overall Steps per Second: 8,370.12105

Timestep Collection Time: 8.43985
Timestep Consumption Time: 3.50788
PPO Batch Consumption Time: 0.49191
Total Iteration Time: 11.94774

Cumulative Model Updates: 12,870
Cumulative Timesteps: 144,550,578

Timesteps Collected: 100,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 46.37666
Policy Entropy: 4.31852
Value Function Loss: 0.00441

Mean KL Divergence: 0.00052
SB3 Clip Fraction: 0.01874
Policy Update Magnitude: 0.09442
Value Function Update Magnitude: 0.15060

Collected Steps per Second: 11,148.95984
Overall Steps per Second: 7,148.82542

Timestep Collection Time: 8.97447
Timestep Consumption Time: 5.02168
PPO Batch Consumption Time: 0.49337
Total Iteration Time: 13.99615

Cumulative Model Updates: 12,876
Cumulative Timesteps: 144,650,634

Timesteps Collected: 100,056
--------END ITERATION REPORT--------


Saving checkpoint 144650634...
Checkpoint 144650634 saved!
