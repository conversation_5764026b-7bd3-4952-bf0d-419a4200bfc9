{"time":"2025-08-04T03:26:45.5772015-05:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T03:26:45.8228842-05:00","level":"INFO","msg":"stream: created new stream","id":"1me7n5we"}
{"time":"2025-08-04T03:26:45.8228842-05:00","level":"INFO","msg":"stream: started","id":"1me7n5we"}
{"time":"2025-08-04T03:26:45.8228842-05:00","level":"INFO","msg":"writer: Do: started","stream_id":"1me7n5we"}
{"time":"2025-08-04T03:26:45.8228842-05:00","level":"INFO","msg":"handler: started","stream_id":"1me7n5we"}
{"time":"2025-08-04T03:26:45.8228842-05:00","level":"INFO","msg":"sender: started","stream_id":"1me7n5we"}
{"time":"2025-08-04T10:35:33.8704885-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-04T10:48:41.0167519-05:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-04T10:48:41.2082685-05:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T10:48:41.2130366-05:00","level":"INFO","msg":"stream: closing","id":"1me7n5we"}
{"time":"2025-08-04T10:48:41.2130366-05:00","level":"INFO","msg":"handler: closed","stream_id":"1me7n5we"}
{"time":"2025-08-04T10:48:41.2130366-05:00","level":"INFO","msg":"writer: Close: closed","stream_id":"1me7n5we"}
{"time":"2025-08-04T10:48:41.2130366-05:00","level":"INFO","msg":"sender: closed","stream_id":"1me7n5we"}
{"time":"2025-08-04T10:48:41.2130366-05:00","level":"INFO","msg":"stream: closed","id":"1me7n5we"}
