_wandb:
    value:
        cli_version: 0.22.0
        e:
            9jtg1tco3i84hcrrx3y9egmv0ld87lp6:
                codePath: example.py
                codePathLocal: example.py
                cpu_count: 8
                cpu_count_logical: 16
                disk:
                    /:
                        total: "499218640896"
                        used: "356007960576"
                email: <EMAIL>
                executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
                host: HHPC
                memory:
                    total: "34282369024"
                os: Windows-10-10.0.26100-SP0
                program: C:\Users\<USER>\Downloads\Rlgym\Rlgym\example.py
                python: CPython 3.11.0
                root: C:\Users\<USER>\Downloads\Rlgym\Rlgym
                startedAt: "2025-09-22T01:48:03.723783Z"
                writerId: 9jtg1tco3i84hcrrx3y9egmv0ld87lp6
        m: []
        python_version: 3.11.0
        t:
            "1":
                - 1
            "2":
                - 1
            "3":
                - 2
                - 13
                - 16
            "4": 3.11.0
            "5": 0.22.0
            "8":
                - 3
            "10":
                - 20
            "12": 0.22.0
            "13": windows-amd64
critic_layer_sizes:
    value:
        - 512
        - 512
        - 512
        - 512
critic_lr:
    value: 0.0002
exp_buffer_size:
    value: 300000
gae_gamma:
    value: 0.99
gae_lambda:
    value: 0.95
min_inference_size:
    value: 29
n_proc:
    value: 32
policy_layer_sizes:
    value:
        - 512
        - 512
        - 512
        - 512
policy_lr:
    value: 0.0002
ppo_batch_size:
    value: 100000
ppo_clip_range:
    value: 0.1
ppo_ent_coef:
    value: 0.01
ppo_epochs:
    value: 1
ppo_minibatch_size:
    value: 50000
shm_buffer_size:
    value: 8192
standardize_obs:
    value: false
standardize_returns:
    value: true
timestep_limit:
    value: 100000000000
ts_per_iteration:
    value: 100000
