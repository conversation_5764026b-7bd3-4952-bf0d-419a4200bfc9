Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,922.28291
Policy Entropy: 3.89976
Value Function Loss: 0.74761

Mean KL Divergence: 0.00377
SB3 Clip Fraction: 0.15318
Policy Update Magnitude: 0.09773
Value Function Update Magnitude: 0.20443

Collected Steps per Second: 12,082.74299
Overall Steps per Second: 6,866.66861

Timestep Collection Time: 8.28140
Timestep Consumption Time: 6.29073
PPO Batch Consumption Time: 1.17641
Total Iteration Time: 14.57213

Cumulative Model Updates: 4,299
Cumulative Timesteps: 48,617,062

Timesteps Collected: 100,062
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,259.22419
Policy Entropy: 3.90330
Value Function Loss: 0.58058

Mean KL Divergence: 0.00795
SB3 Clip Fraction: 0.28648
Policy Update Magnitude: 0.17204
Value Function Update Magnitude: 0.40843

Collected Steps per Second: 12,751.59164
Overall Steps per Second: 8,024.91530

Timestep Collection Time: 7.84404
Timestep Consumption Time: 4.62014
PPO Batch Consumption Time: 0.47217
Total Iteration Time: 12.46418

Cumulative Model Updates: 4,305
Cumulative Timesteps: 48,717,086

Timesteps Collected: 100,024
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,957.79450
Policy Entropy: 3.90804
Value Function Loss: 0.57086

Mean KL Divergence: 0.00939
SB3 Clip Fraction: 0.36234
Policy Update Magnitude: 0.18157
Value Function Update Magnitude: 0.34168

Collected Steps per Second: 12,436.27803
Overall Steps per Second: 7,885.34576

Timestep Collection Time: 8.04244
Timestep Consumption Time: 4.64160
PPO Batch Consumption Time: 0.47375
Total Iteration Time: 12.68403

Cumulative Model Updates: 4,311
Cumulative Timesteps: 48,817,104

Timesteps Collected: 100,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,062.31543
Policy Entropy: 3.92468
Value Function Loss: 0.49630

Mean KL Divergence: 0.02296
SB3 Clip Fraction: 0.49391
Policy Update Magnitude: 0.24142
Value Function Update Magnitude: 0.41824

Collected Steps per Second: 13,073.62210
Overall Steps per Second: 7,377.19597

Timestep Collection Time: 7.65083
Timestep Consumption Time: 5.90771
PPO Batch Consumption Time: 0.46074
Total Iteration Time: 13.55854

Cumulative Model Updates: 4,320
Cumulative Timesteps: 48,917,128

Timesteps Collected: 100,024
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 5,739.53695
Policy Entropy: 3.92830
Value Function Loss: 0.46532

Mean KL Divergence: 0.01964
SB3 Clip Fraction: 0.43546
Policy Update Magnitude: 0.20873
Value Function Update Magnitude: 0.42830

Collected Steps per Second: 13,716.32831
Overall Steps per Second: 7,455.49698

Timestep Collection Time: 7.29291
Timestep Consumption Time: 6.12430
PPO Batch Consumption Time: 0.47419
Total Iteration Time: 13.41721

Cumulative Model Updates: 4,329
Cumulative Timesteps: 49,017,160

Timesteps Collected: 100,032
--------END ITERATION REPORT--------


Saving checkpoint 49017160...
Checkpoint 49017160 saved!
