Created new wandb run! 7<PERSON><PERSON>jt<PERSON><PERSON>
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 88.95432
Policy Entropy: 4.49940
Value Function Loss: nan

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.17724
Value Function Update Magnitude: 0.16801

Collected Steps per Second: 10,752.69095
Overall Steps per Second: 7,676.44529

Timestep Collection Time: 9.30074
Timestep Consumption Time: 3.72716
PPO Batch Consumption Time: 1.54610
Total Iteration Time: 13.02790

Cumulative Model Updates: 1
Cumulative Timesteps: 100,008

Timesteps Collected: 100,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 61.91470
Policy Entropy: 4.49937
Value Function Loss: 245.89930

Mean KL Divergence: 0.00001
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.14285
Value Function Update Magnitude: 0.15282

Collected Steps per Second: 11,564.60447
Overall Steps per Second: 9,428.67343

Timestep Collection Time: 8.64829
Timestep Consumption Time: 1.95915
PPO Batch Consumption Time: 0.45408
Total Iteration Time: 10.60743

Cumulative Model Updates: 2
Cumulative Timesteps: 200,022

Timesteps Collected: 100,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 77.21806
Policy Entropy: 4.49930
Value Function Loss: 162.98228

Mean KL Divergence: 0.00003
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.22790
Value Function Update Magnitude: 0.27943

Collected Steps per Second: 11,270.14307
Overall Steps per Second: 9,052.45842

Timestep Collection Time: 8.87442
Timestep Consumption Time: 2.17407
PPO Batch Consumption Time: 0.29942
Total Iteration Time: 11.04849

Cumulative Model Updates: 4
Cumulative Timesteps: 300,038

Timesteps Collected: 100,016
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 45.35857
Policy Entropy: 4.49913
Value Function Loss: 1.32249

Mean KL Divergence: 0.00007
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.24354
Value Function Update Magnitude: 0.38221

Collected Steps per Second: 10,186.59986
Overall Steps per Second: 8,212.44850

Timestep Collection Time: 9.82055
Timestep Consumption Time: 2.36071
PPO Batch Consumption Time: 0.28109
Total Iteration Time: 12.18126

Cumulative Model Updates: 7
Cumulative Timesteps: 400,076

Timesteps Collected: 100,038
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 80.56269
Policy Entropy: 4.49888
Value Function Loss: 1.39471

Mean KL Divergence: 0.00010
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.16923
Value Function Update Magnitude: 0.37607

Collected Steps per Second: 11,392.74957
Overall Steps per Second: 8,897.58611

Timestep Collection Time: 8.77874
Timestep Consumption Time: 2.46184
PPO Batch Consumption Time: 0.27677
Total Iteration Time: 11.24058

Cumulative Model Updates: 10
Cumulative Timesteps: 500,090

Timesteps Collected: 100,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 52.59588
Policy Entropy: 4.49863
Value Function Loss: 1.41139

Mean KL Divergence: 0.00009
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.13004
Value Function Update Magnitude: 0.37808

Collected Steps per Second: 11,014.05350
Overall Steps per Second: 8,730.55279

Timestep Collection Time: 9.08258
Timestep Consumption Time: 2.37557
PPO Batch Consumption Time: 0.27754
Total Iteration Time: 11.45815

Cumulative Model Updates: 13
Cumulative Timesteps: 600,126

Timesteps Collected: 100,036
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 62.01157
Policy Entropy: 4.49840
Value Function Loss: 1.25307

Mean KL Divergence: 0.00006
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.10766
Value Function Update Magnitude: 0.38399

Collected Steps per Second: 11,447.80400
Overall Steps per Second: 8,997.36650

Timestep Collection Time: 8.73530
Timestep Consumption Time: 2.37906
PPO Batch Consumption Time: 0.27703
Total Iteration Time: 11.11436

Cumulative Model Updates: 16
Cumulative Timesteps: 700,126

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 57.97563
Policy Entropy: 4.49821
Value Function Loss: 1.18436

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.09557
Value Function Update Magnitude: 0.38262

Collected Steps per Second: 9,778.37670
Overall Steps per Second: 7,916.37466

Timestep Collection Time: 10.22971
Timestep Consumption Time: 2.40612
PPO Batch Consumption Time: 0.27666
Total Iteration Time: 12.63583

Cumulative Model Updates: 19
Cumulative Timesteps: 800,156

Timesteps Collected: 100,030
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 61.45218
Policy Entropy: 4.49806
Value Function Loss: 1.13040

Mean KL Divergence: 0.00003
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.08932
Value Function Update Magnitude: 0.36527

Collected Steps per Second: 11,125.88887
Overall Steps per Second: 8,826.49761

Timestep Collection Time: 8.98859
Timestep Consumption Time: 2.34162
PPO Batch Consumption Time: 0.27789
Total Iteration Time: 11.33020

Cumulative Model Updates: 22
Cumulative Timesteps: 900,162

Timesteps Collected: 100,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 89.74081
Policy Entropy: 4.49796
Value Function Loss: 1.11817

Mean KL Divergence: 0.00003
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.08715
Value Function Update Magnitude: 0.33807

Collected Steps per Second: 11,199.09753
Overall Steps per Second: 8,746.47725

Timestep Collection Time: 8.93090
Timestep Consumption Time: 2.50433
PPO Batch Consumption Time: 0.27497
Total Iteration Time: 11.43523

Cumulative Model Updates: 25
Cumulative Timesteps: 1,000,180

Timesteps Collected: 100,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 82.97949
Policy Entropy: 4.49786
Value Function Loss: 1.10206

Mean KL Divergence: 0.00003
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.08719
Value Function Update Magnitude: 0.32848

Collected Steps per Second: 12,218.29683
Overall Steps per Second: 9,578.45849

Timestep Collection Time: 8.18690
Timestep Consumption Time: 2.25632
PPO Batch Consumption Time: 0.27072
Total Iteration Time: 10.44323

Cumulative Model Updates: 28
Cumulative Timesteps: 1,100,210

Timesteps Collected: 100,030
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 38.93408
Policy Entropy: 4.49778
Value Function Loss: 1.10601

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.08765
Value Function Update Magnitude: 0.30364

Collected Steps per Second: 12,792.58203
Overall Steps per Second: 9,968.59593

Timestep Collection Time: 7.81859
Timestep Consumption Time: 2.21492
PPO Batch Consumption Time: 0.26888
Total Iteration Time: 10.03351

Cumulative Model Updates: 31
Cumulative Timesteps: 1,200,230

Timesteps Collected: 100,020
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 58.17102
Policy Entropy: 4.49768
Value Function Loss: 1.14931

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.08774
Value Function Update Magnitude: 0.28938

Collected Steps per Second: 12,935.33431
Overall Steps per Second: 10,013.90079

Timestep Collection Time: 7.73277
Timestep Consumption Time: 2.25594
PPO Batch Consumption Time: 0.26903
Total Iteration Time: 9.98871

Cumulative Model Updates: 34
Cumulative Timesteps: 1,300,256

Timesteps Collected: 100,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 89.72700
Policy Entropy: 4.49754
Value Function Loss: 1.20068

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.09120
Value Function Update Magnitude: 0.28757

Collected Steps per Second: 12,960.44157
Overall Steps per Second: 10,034.60873

Timestep Collection Time: 7.71687
Timestep Consumption Time: 2.25004
PPO Batch Consumption Time: 0.26997
Total Iteration Time: 9.96691

Cumulative Model Updates: 37
Cumulative Timesteps: 1,400,270

Timesteps Collected: 100,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 54.53457
Policy Entropy: 4.49729
Value Function Loss: 1.21586

Mean KL Divergence: 0.00006
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.09744
Value Function Update Magnitude: 0.28118

Collected Steps per Second: 12,933.11553
Overall Steps per Second: 10,029.48848

Timestep Collection Time: 7.73441
Timestep Consumption Time: 2.23918
PPO Batch Consumption Time: 0.26804
Total Iteration Time: 9.97359

Cumulative Model Updates: 40
Cumulative Timesteps: 1,500,300

Timesteps Collected: 100,030
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 53.07559
Policy Entropy: 4.49691
Value Function Loss: 1.25890

Mean KL Divergence: 0.00008
SB3 Clip Fraction: 0.00013
Policy Update Magnitude: 0.10122
Value Function Update Magnitude: 0.27277

Collected Steps per Second: 12,940.79904
Overall Steps per Second: 10,038.01771

Timestep Collection Time: 7.72904
Timestep Consumption Time: 2.23508
PPO Batch Consumption Time: 0.26916
Total Iteration Time: 9.96412

Cumulative Model Updates: 43
Cumulative Timesteps: 1,600,320

Timesteps Collected: 100,020
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 69.89258
Policy Entropy: 4.49630
Value Function Loss: 1.30770

Mean KL Divergence: 0.00013
SB3 Clip Fraction: 0.00100
Policy Update Magnitude: 0.10919
Value Function Update Magnitude: 0.25714

Collected Steps per Second: 12,826.71308
Overall Steps per Second: 9,967.01645

Timestep Collection Time: 7.79950
Timestep Consumption Time: 2.23780
PPO Batch Consumption Time: 0.27170
Total Iteration Time: 10.03731

Cumulative Model Updates: 46
Cumulative Timesteps: 1,700,362

Timesteps Collected: 100,042
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 66.52585
Policy Entropy: 4.49539
Value Function Loss: 1.32588

Mean KL Divergence: 0.00020
SB3 Clip Fraction: 0.00204
Policy Update Magnitude: 0.11687
Value Function Update Magnitude: 0.26112

Collected Steps per Second: 12,760.11762
Overall Steps per Second: 9,899.21131

Timestep Collection Time: 7.83833
Timestep Consumption Time: 2.26530
PPO Batch Consumption Time: 0.26978
Total Iteration Time: 10.10363

Cumulative Model Updates: 49
Cumulative Timesteps: 1,800,380

Timesteps Collected: 100,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 36.08578
Policy Entropy: 4.49410
Value Function Loss: 1.33249

Mean KL Divergence: 0.00030
SB3 Clip Fraction: 0.00286
Policy Update Magnitude: 0.12240
Value Function Update Magnitude: 0.23082

Collected Steps per Second: 12,438.87459
Overall Steps per Second: 9,342.71976

Timestep Collection Time: 8.03931
Timestep Consumption Time: 2.66421
PPO Batch Consumption Time: 0.28481
Total Iteration Time: 10.70352

Cumulative Model Updates: 52
Cumulative Timesteps: 1,900,380

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 85.15405
Policy Entropy: 4.49255
Value Function Loss: 1.30881

Mean KL Divergence: 0.00040
SB3 Clip Fraction: 0.00502
Policy Update Magnitude: 0.12225
Value Function Update Magnitude: 0.19862

Collected Steps per Second: 10,613.84958
Overall Steps per Second: 8,075.23185

Timestep Collection Time: 9.42467
Timestep Consumption Time: 2.96284
PPO Batch Consumption Time: 0.36811
Total Iteration Time: 12.38751

Cumulative Model Updates: 55
Cumulative Timesteps: 2,000,412

Timesteps Collected: 100,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 51.88470
Policy Entropy: 4.49089
Value Function Loss: 1.33037

Mean KL Divergence: 0.00042
SB3 Clip Fraction: 0.00596
Policy Update Magnitude: 0.11985
Value Function Update Magnitude: 0.18004

Collected Steps per Second: 10,312.77124
Overall Steps per Second: 8,382.88411

Timestep Collection Time: 9.69924
Timestep Consumption Time: 2.23293
PPO Batch Consumption Time: 0.26890
Total Iteration Time: 11.93217

Cumulative Model Updates: 58
Cumulative Timesteps: 2,100,438

Timesteps Collected: 100,026
--------END ITERATION REPORT--------


Saving checkpoint 2100438...


LEARNING LOOP ENCOUNTERED AN ERROR
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Rlgym\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\Rlgym\Rlgym\rlgym_ppo\learner.py", line 322, in _learn
    self.save(self.agent.cumulative_timesteps)
  File "C:\Users\<USER>\Downloads\Rlgym\Rlgym\rlgym_ppo\learner.py", line 454, in save
    json.dump(book_keeping_vars, f, indent=4)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type float32 is not JSON serializable

Saving checkpoint 2100438...
FAILED TO SAVE ON EXIT
