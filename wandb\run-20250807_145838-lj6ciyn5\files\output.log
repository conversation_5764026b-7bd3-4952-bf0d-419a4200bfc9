Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 216.52712
Policy Entropy: 4.07023
Value Function Loss: 0.02700

Mean KL Divergence: 0.00018
SB3 Clip Fraction: 0.00453
Policy Update Magnitude: 0.05019
Value Function Update Magnitude: 0.09080

Collected Steps per Second: 13,054.58553
Overall Steps per Second: 7,276.30789

Timestep Collection Time: 7.66459
Timestep Consumption Time: 6.08662
PPO Batch Consumption Time: 1.11235
Total Iteration Time: 13.75120

Cumulative Model Updates: 6,747
Cumulative Timesteps: 76,025,704

Timesteps Collected: 100,058
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 526.90308
Policy Entropy: 4.07510
Value Function Loss: 0.02326

Mean KL Divergence: 0.00089
SB3 Clip Fraction: 0.03664
Policy Update Magnitude: 0.10719
Value Function Update Magnitude: 0.19366

Collected Steps per Second: 13,690.27642
Overall Steps per Second: 8,441.12861

Timestep Collection Time: 7.30460
Timestep Consumption Time: 4.54239
PPO Batch Consumption Time: 0.46845
Total Iteration Time: 11.84699

Cumulative Model Updates: 6,753
Cumulative Timesteps: 76,125,706

Timesteps Collected: 100,002
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 622.19310
Policy Entropy: 4.08170
Value Function Loss: 0.02021

Mean KL Divergence: 0.00115
SB3 Clip Fraction: 0.04810
Policy Update Magnitude: 0.14893
Value Function Update Magnitude: 0.27746

Collected Steps per Second: 13,743.30456
Overall Steps per Second: 7,546.38417

Timestep Collection Time: 7.28035
Timestep Consumption Time: 5.97846
PPO Batch Consumption Time: 0.46823
Total Iteration Time: 13.25880

Cumulative Model Updates: 6,762
Cumulative Timesteps: 76,225,762

Timesteps Collected: 100,056
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,057.34468
Policy Entropy: 4.08388
Value Function Loss: 0.01879

Mean KL Divergence: 0.00093
SB3 Clip Fraction: 0.03466
Policy Update Magnitude: 0.13244
Value Function Update Magnitude: 0.26243

Collected Steps per Second: 13,768.11340
Overall Steps per Second: 7,555.31119

Timestep Collection Time: 7.26330
Timestep Consumption Time: 5.97268
PPO Batch Consumption Time: 0.46892
Total Iteration Time: 13.23599

Cumulative Model Updates: 6,771
Cumulative Timesteps: 76,325,764

Timesteps Collected: 100,002
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 661.73797
Policy Entropy: 4.08392
Value Function Loss: 0.01991

Mean KL Divergence: 0.00086
SB3 Clip Fraction: 0.03119
Policy Update Magnitude: 0.13098
Value Function Update Magnitude: 0.26893

Collected Steps per Second: 13,754.23368
Overall Steps per Second: 7,471.14177

Timestep Collection Time: 7.27107
Timestep Consumption Time: 6.11484
PPO Batch Consumption Time: 0.48097
Total Iteration Time: 13.38591

Cumulative Model Updates: 6,780
Cumulative Timesteps: 76,425,772

Timesteps Collected: 100,008
--------END ITERATION REPORT--------


Saving checkpoint 76425772...
Checkpoint 76425772 saved!
