{"time":"2025-08-08T01:32:13.1189257-05:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-08T01:32:13.3857128-05:00","level":"INFO","msg":"stream: created new stream","id":"lj6ciyn5"}
{"time":"2025-08-08T01:32:13.3857128-05:00","level":"INFO","msg":"stream: started","id":"lj6ciyn5"}
{"time":"2025-08-08T01:32:13.3857128-05:00","level":"INFO","msg":"writer: Do: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-08T01:32:13.3857128-05:00","level":"INFO","msg":"handler: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-08T01:32:13.3857128-05:00","level":"INFO","msg":"sender: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-08T01:56:43.8941715-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:56:58.8944824-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:57:13.8946545-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:57:28.8948904-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:57:43.8950254-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:57:58.8957146-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:58:13.8958655-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:58:28.8960348-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:58:43.8962409-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:58:58.8963676-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:59:13.8970603-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:59:28.897245-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:59:43.8973733-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T01:59:58.897538-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:00:13.8976881-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:00:28.8984312-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:00:43.8983588-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:00:58.8986974-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:01:13.8988681-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:01:28.8993376-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:01:43.8997333-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:01:58.8998854-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:02:13.9000959-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:02:28.9003911-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:02:43.9005376-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:02:58.9010983-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:03:13.9012418-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:03:28.9014368-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:03:43.9015487-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:03:58.9022242-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:04:13.9023787-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:04:28.9025403-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:04:43.9027145-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:04:58.9032865-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:05:13.9034442-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:05:28.9037162-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:05:43.9038716-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:05:58.9040578-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:06:13.9042368-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:06:28.9049418-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:06:43.9049342-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:06:58.9055094-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:07:13.9056689-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:07:28.9058276-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:07:43.9059783-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:07:58.906141-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:08:13.9069052-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:08:28.9070834-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:08:43.9072541-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:08:58.9073972-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:09:13.9085525-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:09:28.9077451-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:09:43.9084632-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:09:58.9086371-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:10:13.9087896-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:10:28.9089856-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:10:43.9091589-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:10:58.9098422-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:11:13.9099889-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:11:28.9101895-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:11:43.9103982-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:11:58.9105956-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:12:13.9109603-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:12:28.9113341-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:12:43.9112515-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:12:58.911935-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:13:13.912173-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:13:28.9120825-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:13:43.9127912-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:13:58.9130178-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:14:13.9129713-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-08-08T02:14:28.9131444-05:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
