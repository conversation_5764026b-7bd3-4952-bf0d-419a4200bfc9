Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 2,191.79721
Policy Entropy: 3.13295
Value Function Loss: 2.69198

Mean KL Divergence: 0.00154
SB3 Clip Fraction: 0.08661
Policy Update Magnitude: 0.13101
Value Function Update Magnitude: 0.21106

Collected Steps per Second: 12,453.72628
Overall Steps per Second: 6,471.82574

Timestep Collection Time: 4.01807
Timestep Consumption Time: 3.71390
PPO Batch Consumption Time: 0.75327
Total Iteration Time: 7.73198

Cumulative Model Updates: 4,029
Cumulative Timesteps: 22,506,992

Timesteps Collected: 50,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,417.64066
Policy Entropy: 3.14502
Value Function Loss: 2.45281

Mean KL Divergence: 0.00276
SB3 Clip Fraction: 0.14137
Policy Update Magnitude: 0.28849
Value Function Update Magnitude: 0.45193

Collected Steps per Second: 13,199.95818
Overall Steps per Second: 8,218.04260

Timestep Collection Time: 3.79092
Timestep Consumption Time: 2.29812
PPO Batch Consumption Time: 0.24587
Total Iteration Time: 6.08904

Cumulative Model Updates: 4,035
Cumulative Timesteps: 22,557,032

Timesteps Collected: 50,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 7,179.98121
Policy Entropy: 3.15332
Value Function Loss: 2.35187

Mean KL Divergence: 0.00370
SB3 Clip Fraction: 0.19717
Policy Update Magnitude: 0.39577
Value Function Update Magnitude: 0.51840

Collected Steps per Second: 13,392.86910
Overall Steps per Second: 7,364.03698

Timestep Collection Time: 3.73423
Timestep Consumption Time: 3.05716
PPO Batch Consumption Time: 0.24645
Total Iteration Time: 6.79138

Cumulative Model Updates: 4,044
Cumulative Timesteps: 22,607,044

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,276.05051
Policy Entropy: 3.16373
Value Function Loss: 2.29453

Mean KL Divergence: 0.00301
SB3 Clip Fraction: 0.16921
Policy Update Magnitude: 0.35621
Value Function Update Magnitude: 0.46459

Collected Steps per Second: 13,272.54554
Overall Steps per Second: 7,327.48816

Timestep Collection Time: 3.76913
Timestep Consumption Time: 3.05804
PPO Batch Consumption Time: 0.24701
Total Iteration Time: 6.82717

Cumulative Model Updates: 4,053
Cumulative Timesteps: 22,657,070

Timesteps Collected: 50,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,813.12882
Policy Entropy: 3.16689
Value Function Loss: 2.31961

Mean KL Divergence: 0.00407
SB3 Clip Fraction: 0.21590
Policy Update Magnitude: 0.34472
Value Function Update Magnitude: 0.43865

Collected Steps per Second: 13,231.77176
Overall Steps per Second: 7,083.72447

Timestep Collection Time: 3.78014
Timestep Consumption Time: 3.28083
PPO Batch Consumption Time: 0.26198
Total Iteration Time: 7.06097

Cumulative Model Updates: 4,062
Cumulative Timesteps: 22,707,088

Timesteps Collected: 50,018
--------END ITERATION REPORT--------


Saving checkpoint 22707088...
Checkpoint 22707088 saved!
