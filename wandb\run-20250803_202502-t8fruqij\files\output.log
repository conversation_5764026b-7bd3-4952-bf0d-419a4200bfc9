Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 2,067.04300
Policy Entropy: 3.33162
Value Function Loss: 0.71369

Mean KL Divergence: 0.00060
SB3 Clip Fraction: 0.02361
Policy Update Magnitude: 0.15329
Value Function Update Magnitude: 0.18008

Collected Steps per Second: 10,116.28058
Overall Steps per Second: 5,704.31412

Timestep Collection Time: 4.94253
Timestep Consumption Time: 3.82277
PPO Batch Consumption Time: 0.75800
Total Iteration Time: 8.76530

Cumulative Model Updates: 5,961
Cumulative Timesteps: 33,414,180

Timesteps Collected: 50,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,659.62533
Policy Entropy: 3.34477
Value Function Loss: 0.60965

Mean KL Divergence: 0.00183
SB3 Clip Fraction: 0.09968
Policy Update Magnitude: 0.18168
Value Function Update Magnitude: 0.19533

Collected Steps per Second: 11,304.37495
Overall Steps per Second: 8,070.86949

Timestep Collection Time: 4.42377
Timestep Consumption Time: 1.77234
PPO Batch Consumption Time: 0.27498
Total Iteration Time: 6.19611

Cumulative Model Updates: 5,964
Cumulative Timesteps: 33,464,188

Timesteps Collected: 50,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,489.70444
Policy Entropy: 3.35711
Value Function Loss: 0.53159

Mean KL Divergence: 0.00268
SB3 Clip Fraction: 0.14958
Policy Update Magnitude: 0.31749
Value Function Update Magnitude: 0.37500

Collected Steps per Second: 10,992.24701
Overall Steps per Second: 6,953.02469

Timestep Collection Time: 4.55485
Timestep Consumption Time: 2.64605
PPO Batch Consumption Time: 0.27750
Total Iteration Time: 7.20089

Cumulative Model Updates: 5,970
Cumulative Timesteps: 33,514,256

Timesteps Collected: 50,068
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 5,861.55435
Policy Entropy: 3.37440
Value Function Loss: 0.46346

Mean KL Divergence: 0.00227
SB3 Clip Fraction: 0.12381
Policy Update Magnitude: 0.39725
Value Function Update Magnitude: 0.55164

Collected Steps per Second: 10,837.97716
Overall Steps per Second: 6,123.05023

Timestep Collection Time: 4.61581
Timestep Consumption Time: 3.55430
PPO Batch Consumption Time: 0.28284
Total Iteration Time: 8.17011

Cumulative Model Updates: 5,979
Cumulative Timesteps: 33,564,282

Timesteps Collected: 50,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 5,825.88485
Policy Entropy: 3.37307
Value Function Loss: 0.44942

Mean KL Divergence: 0.00214
SB3 Clip Fraction: 0.11455
Policy Update Magnitude: 0.37935
Value Function Update Magnitude: 0.45723

Collected Steps per Second: 11,086.75855
Overall Steps per Second: 6,197.96027

Timestep Collection Time: 4.51584
Timestep Consumption Time: 3.56198
PPO Batch Consumption Time: 0.28074
Total Iteration Time: 8.07782

Cumulative Model Updates: 5,988
Cumulative Timesteps: 33,614,348

Timesteps Collected: 50,066
--------END ITERATION REPORT--------


Saving checkpoint 33614348...
Checkpoint 33614348 saved!
