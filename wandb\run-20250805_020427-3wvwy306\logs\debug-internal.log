{"time":"2025-08-05T02:04:28.2730644-05:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T02:04:28.6268801-05:00","level":"INFO","msg":"stream: created new stream","id":"3wvwy306"}
{"time":"2025-08-05T02:04:28.6268801-05:00","level":"INFO","msg":"stream: started","id":"3wvwy306"}
{"time":"2025-08-05T02:04:28.6268801-05:00","level":"INFO","msg":"handler: started","stream_id":"3wvwy306"}
{"time":"2025-08-05T02:04:28.6268801-05:00","level":"INFO","msg":"writer: Do: started","stream_id":"3wvwy306"}
{"time":"2025-08-05T02:04:28.6268801-05:00","level":"INFO","msg":"sender: started","stream_id":"3wvwy306"}
{"time":"2025-08-05T02:23:19.6539271-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": read tcp *********:64380->*************:443: wsarecv: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."}
{"time":"2025-08-05T09:08:01.2072318-05:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/graphql","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-05T09:11:30.578612-05:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/graphql","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-05T09:15:03.1340128-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-05T12:40:57.9700245-05:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-05T12:40:58.1141682-05:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading history steps 13528-13529, summary, console lines 135960-135988","runtime_seconds":0.1441437}],"total_operations":1}}
{"time":"2025-08-05T12:40:58.662765-05:00","level":"INFO","msg":"stream: closing","id":"3wvwy306"}
{"time":"2025-08-05T12:40:58.662765-05:00","level":"INFO","msg":"handler: closed","stream_id":"3wvwy306"}
{"time":"2025-08-05T12:40:58.662765-05:00","level":"INFO","msg":"sender: closed","stream_id":"3wvwy306"}
{"time":"2025-08-05T12:40:58.662765-05:00","level":"INFO","msg":"writer: Close: closed","stream_id":"3wvwy306"}
{"time":"2025-08-05T12:40:58.662765-05:00","level":"INFO","msg":"stream: closed","id":"3wvwy306"}
