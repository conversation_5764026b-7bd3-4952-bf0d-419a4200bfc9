Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 227.17254
Policy Entropy: 4.49879
Value Function Loss: 0.98704

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.04048
Value Function Update Magnitude: 0.07295

Collected Steps per Second: 11,424.36578
Overall Steps per Second: 8,591.81656

Timestep Collection Time: 8.75585
Timestep Consumption Time: 2.88663
PPO Batch Consumption Time: 0.74987
Total Iteration Time: 11.64247

Cumulative Model Updates: 6
Cumulative Timesteps: 400,084

Timesteps Collected: 100,030
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 279.62537
Policy Entropy: 4.49876
Value Function Loss: 0.71923

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.06743
Value Function Update Magnitude: 0.14267

Collected Steps per Second: 12,190.44568
Overall Steps per Second: 9,245.41588

Timestep Collection Time: 8.20462
Timestep Consumption Time: 2.61350
PPO Batch Consumption Time: 0.40406
Total Iteration Time: 10.81812

Cumulative Model Updates: 8
Cumulative Timesteps: 500,102

Timesteps Collected: 100,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 180.67465
Policy Entropy: 4.49871
Value Function Loss: 0.54291

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.05408
Value Function Update Magnitude: 0.14021

Collected Steps per Second: 12,435.78740
Overall Steps per Second: 9,866.81185

Timestep Collection Time: 8.04276
Timestep Consumption Time: 2.09405
PPO Batch Consumption Time: 0.13796
Total Iteration Time: 10.13681

Cumulative Model Updates: 10
Cumulative Timesteps: 600,120

Timesteps Collected: 100,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 168.72716
Policy Entropy: 4.49866
Value Function Loss: 0.63592

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.04488
Value Function Update Magnitude: 0.13883

Collected Steps per Second: 12,446.81175
Overall Steps per Second: 9,877.01506

Timestep Collection Time: 8.03756
Timestep Consumption Time: 2.09121
PPO Batch Consumption Time: 0.14300
Total Iteration Time: 10.12877

Cumulative Model Updates: 12
Cumulative Timesteps: 700,162

Timesteps Collected: 100,042
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 222.46564
Policy Entropy: 4.49862
Value Function Loss: 0.73097

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.03884
Value Function Update Magnitude: 0.13841

Collected Steps per Second: 11,932.30192
Overall Steps per Second: 9,510.38826

Timestep Collection Time: 8.38179
Timestep Consumption Time: 2.13450
PPO Batch Consumption Time: 0.13901
Total Iteration Time: 10.51629

Cumulative Model Updates: 14
Cumulative Timesteps: 800,176

Timesteps Collected: 100,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 189.99387
Policy Entropy: 4.49858
Value Function Loss: 0.79185

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.03477
Value Function Update Magnitude: 0.13867

Collected Steps per Second: 12,429.15109
Overall Steps per Second: 9,508.34602

Timestep Collection Time: 8.04737
Timestep Consumption Time: 2.47202
PPO Batch Consumption Time: 0.15986
Total Iteration Time: 10.51939

Cumulative Model Updates: 16
Cumulative Timesteps: 900,198

Timesteps Collected: 100,022
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 243.17363
Policy Entropy: 4.49853
Value Function Loss: 0.83471

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.03206
Value Function Update Magnitude: 0.13916

Collected Steps per Second: 10,374.34248
Overall Steps per Second: 7,471.05655

Timestep Collection Time: 9.63917
Timestep Consumption Time: 3.74582
PPO Batch Consumption Time: 0.41798
Total Iteration Time: 13.38499

Cumulative Model Updates: 18
Cumulative Timesteps: 1,000,198

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 238.24268
Policy Entropy: 4.49848
Value Function Loss: 0.94949

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.03024
Value Function Update Magnitude: 0.14001

Collected Steps per Second: 9,103.90977
Overall Steps per Second: 7,601.68490

Timestep Collection Time: 10.98649
Timestep Consumption Time: 2.17112
PPO Batch Consumption Time: 0.14400
Total Iteration Time: 13.15761

Cumulative Model Updates: 20
Cumulative Timesteps: 1,100,218

Timesteps Collected: 100,020
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 294.69287
Policy Entropy: 4.49843
Value Function Loss: 1.02716

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.02968
Value Function Update Magnitude: 0.13991

Collected Steps per Second: 12,417.37195
Overall Steps per Second: 9,883.69043

Timestep Collection Time: 8.05388
Timestep Consumption Time: 2.06461
PPO Batch Consumption Time: 0.14250
Total Iteration Time: 10.11849

Cumulative Model Updates: 22
Cumulative Timesteps: 1,200,226

Timesteps Collected: 100,008
--------END ITERATION REPORT--------


Saving checkpoint 1200226...
Checkpoint 1200226 saved!
