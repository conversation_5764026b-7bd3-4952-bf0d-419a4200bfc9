Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 387.64176
Policy Entropy: 4.06534
Value Function Loss: 0.03004

Mean KL Divergence: 0.00018
SB3 Clip Fraction: 0.00312
Policy Update Magnitude: 0.06294
Value Function Update Magnitude: 0.10805

Collected Steps per Second: 14,036.47876
Overall Steps per Second: 7,861.20644

Timestep Collection Time: 7.12429
Timestep Consumption Time: 5.59640
PPO Batch Consumption Time: 1.00809
Total Iteration Time: 12.72069

Cumulative Model Updates: 6,651
Cumulative Timesteps: 74,825,308

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 522.71115
Policy Entropy: 4.06960
Value Function Loss: 0.02569

Mean KL Divergence: 0.00102
SB3 Clip Fraction: 0.04246
Policy Update Magnitude: 0.12150
Value Function Update Magnitude: 0.22122

Collected Steps per Second: 14,979.41289
Overall Steps per Second: 8,965.31564

Timestep Collection Time: 6.67930
Timestep Consumption Time: 4.48060
PPO Batch Consumption Time: 0.46097
Total Iteration Time: 11.15990

Cumulative Model Updates: 6,657
Cumulative Timesteps: 74,925,360

Timesteps Collected: 100,052
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 790.18834
Policy Entropy: 4.07702
Value Function Loss: 0.02257

Mean KL Divergence: 0.00135
SB3 Clip Fraction: 0.06453
Policy Update Magnitude: 0.11353
Value Function Update Magnitude: 0.21330

Collected Steps per Second: 14,597.88871
Overall Steps per Second: 8,785.41764

Timestep Collection Time: 6.85072
Timestep Consumption Time: 4.53246
PPO Batch Consumption Time: 0.46328
Total Iteration Time: 11.38318

Cumulative Model Updates: 6,663
Cumulative Timesteps: 75,025,366

Timesteps Collected: 100,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 953.65436
Policy Entropy: 4.08134
Value Function Loss: 0.01833

Mean KL Divergence: 0.00109
SB3 Clip Fraction: 0.04409
Policy Update Magnitude: 0.13982
Value Function Update Magnitude: 0.24951

Collected Steps per Second: 13,511.62354
Overall Steps per Second: 7,487.94664

Timestep Collection Time: 7.40178
Timestep Consumption Time: 5.95436
PPO Batch Consumption Time: 0.46494
Total Iteration Time: 13.35613

Cumulative Model Updates: 6,672
Cumulative Timesteps: 75,125,376

Timesteps Collected: 100,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 731.64764
Policy Entropy: 4.08419
Value Function Loss: 0.01689

Mean KL Divergence: 0.00101
SB3 Clip Fraction: 0.03772
Policy Update Magnitude: 0.12919
Value Function Update Magnitude: 0.23020

Collected Steps per Second: 13,620.84091
Overall Steps per Second: 7,505.97381

Timestep Collection Time: 7.34419
Timestep Consumption Time: 5.98306
PPO Batch Consumption Time: 0.46692
Total Iteration Time: 13.32725

Cumulative Model Updates: 6,681
Cumulative Timesteps: 75,225,410

Timesteps Collected: 100,034
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 606.46138
Policy Entropy: 4.08111
Value Function Loss: 0.01758

Mean KL Divergence: 0.00097
SB3 Clip Fraction: 0.03655
Policy Update Magnitude: 0.12431
Value Function Update Magnitude: 0.21996

Collected Steps per Second: 13,710.69918
Overall Steps per Second: 7,535.80468

Timestep Collection Time: 7.29547
Timestep Consumption Time: 5.97796
PPO Batch Consumption Time: 0.46741
Total Iteration Time: 13.27343

Cumulative Model Updates: 6,690
Cumulative Timesteps: 75,325,436

Timesteps Collected: 100,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,048.48355
Policy Entropy: 4.08076
Value Function Loss: 0.01813

Mean KL Divergence: 0.00089
SB3 Clip Fraction: 0.03090
Policy Update Magnitude: 0.12196
Value Function Update Magnitude: 0.21426

Collected Steps per Second: 13,737.47933
Overall Steps per Second: 7,559.01271

Timestep Collection Time: 7.28401
Timestep Consumption Time: 5.95369
PPO Batch Consumption Time: 0.46675
Total Iteration Time: 13.23771

Cumulative Model Updates: 6,699
Cumulative Timesteps: 75,425,500

Timesteps Collected: 100,064
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,480.99206
Policy Entropy: 4.08064
Value Function Loss: 0.01940

Mean KL Divergence: 0.00092
SB3 Clip Fraction: 0.03326
Policy Update Magnitude: 0.12909
Value Function Update Magnitude: 0.22381

Collected Steps per Second: 13,648.32889
Overall Steps per Second: 7,537.03835

Timestep Collection Time: 7.32910
Timestep Consumption Time: 5.94269
PPO Batch Consumption Time: 0.46609
Total Iteration Time: 13.27179

Cumulative Model Updates: 6,708
Cumulative Timesteps: 75,525,530

Timesteps Collected: 100,030
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 683.86331
Policy Entropy: 4.08200
Value Function Loss: 0.01831

Mean KL Divergence: 0.00088
SB3 Clip Fraction: 0.03122
Policy Update Magnitude: 0.12923
Value Function Update Magnitude: 0.22440

Collected Steps per Second: 13,640.17783
Overall Steps per Second: 7,524.10493

Timestep Collection Time: 7.33231
Timestep Consumption Time: 5.96017
PPO Batch Consumption Time: 0.46856
Total Iteration Time: 13.29248

Cumulative Model Updates: 6,717
Cumulative Timesteps: 75,625,544

Timesteps Collected: 100,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 639.84590
Policy Entropy: 4.08177
Value Function Loss: 0.01874

Mean KL Divergence: 0.00085
SB3 Clip Fraction: 0.02885
Policy Update Magnitude: 0.12479
Value Function Update Magnitude: 0.22250

Collected Steps per Second: 13,704.61188
Overall Steps per Second: 7,539.26274

Timestep Collection Time: 7.29754
Timestep Consumption Time: 5.96768
PPO Batch Consumption Time: 0.46485
Total Iteration Time: 13.26522

Cumulative Model Updates: 6,726
Cumulative Timesteps: 75,725,554

Timesteps Collected: 100,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 594.08812
Policy Entropy: 4.07932
Value Function Loss: 0.01835

Mean KL Divergence: 0.00091
SB3 Clip Fraction: 0.03314
Policy Update Magnitude: 0.12727
Value Function Update Magnitude: 0.23251

Collected Steps per Second: 13,760.24789
Overall Steps per Second: 7,561.93179

Timestep Collection Time: 7.27065
Timestep Consumption Time: 5.95956
PPO Batch Consumption Time: 0.46627
Total Iteration Time: 13.23022

Cumulative Model Updates: 6,735
Cumulative Timesteps: 75,825,600

Timesteps Collected: 100,046
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 803.80671
Policy Entropy: 4.08059
Value Function Loss: 0.01827

Mean KL Divergence: 0.00092
SB3 Clip Fraction: 0.03356
Policy Update Magnitude: 0.12875
Value Function Update Magnitude: 0.24120

Collected Steps per Second: 13,648.07363
Overall Steps per Second: 7,446.51916

Timestep Collection Time: 7.33041
Timestep Consumption Time: 6.10486
PPO Batch Consumption Time: 0.47858
Total Iteration Time: 13.43527

Cumulative Model Updates: 6,744
Cumulative Timesteps: 75,925,646

Timesteps Collected: 100,046
--------END ITERATION REPORT--------


Saving checkpoint 75925646...
Checkpoint 75925646 saved!
