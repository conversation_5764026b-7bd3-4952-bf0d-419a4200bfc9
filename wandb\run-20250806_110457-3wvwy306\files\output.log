Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,499.84898
Policy Entropy: 3.95959
Value Function Loss: 0.10783

Mean KL Divergence: 0.00046
SB3 Clip Fraction: 0.01383
Policy Update Magnitude: 0.09378
Value Function Update Magnitude: 0.12248

Collected Steps per Second: 11,313.33907
Overall Steps per Second: 6,588.54306

Timestep Collection Time: 8.84407
Timestep Consumption Time: 6.34229
PPO Batch Consumption Time: 1.12064
Total Iteration Time: 15.18636

Cumulative Model Updates: 19,734
Cumulative Timesteps: 111,730,450

Timesteps Collected: 100,056
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,226.09424
Policy Entropy: 3.96454
Value Function Loss: 0.09723

Mean KL Divergence: 0.00339
SB3 Clip Fraction: 0.14510
Policy Update Magnitude: 0.21841
Value Function Update Magnitude: 0.32262

Collected Steps per Second: 12,240.81939
Overall Steps per Second: 7,799.03364

Timestep Collection Time: 8.17413
Timestep Consumption Time: 4.65541
PPO Batch Consumption Time: 0.47592
Total Iteration Time: 12.82954

Cumulative Model Updates: 19,740
Cumulative Timesteps: 111,830,508

Timesteps Collected: 100,058
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,078.13883
Policy Entropy: 3.96876
Value Function Loss: 0.08663

Mean KL Divergence: 0.00404
SB3 Clip Fraction: 0.17796
Policy Update Magnitude: 0.29271
Value Function Update Magnitude: 0.43612

Collected Steps per Second: 12,324.68599
Overall Steps per Second: 7,030.61770

Timestep Collection Time: 8.11704
Timestep Consumption Time: 6.11215
PPO Batch Consumption Time: 0.47710
Total Iteration Time: 14.22919

Cumulative Model Updates: 19,749
Cumulative Timesteps: 111,930,548

Timesteps Collected: 100,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 5,364.46457
Policy Entropy: 3.97414
Value Function Loss: 0.07910

Mean KL Divergence: 0.00329
SB3 Clip Fraction: 0.15060
Policy Update Magnitude: 0.25912
Value Function Update Magnitude: 0.41293

Collected Steps per Second: 12,281.89912
Overall Steps per Second: 7,011.66410

Timestep Collection Time: 8.14288
Timestep Consumption Time: 6.12050
PPO Batch Consumption Time: 0.47752
Total Iteration Time: 14.26338

Cumulative Model Updates: 19,758
Cumulative Timesteps: 112,030,558

Timesteps Collected: 100,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 5,615.45837
Policy Entropy: 3.97149
Value Function Loss: 0.07948

Mean KL Divergence: 0.00209
SB3 Clip Fraction: 0.09768
Policy Update Magnitude: 0.25310
Value Function Update Magnitude: 0.48311

Collected Steps per Second: 12,106.26636
Overall Steps per Second: 6,944.90525

Timestep Collection Time: 8.26398
Timestep Consumption Time: 6.14168
PPO Batch Consumption Time: 0.47529
Total Iteration Time: 14.40567

Cumulative Model Updates: 19,767
Cumulative Timesteps: 112,130,604

Timesteps Collected: 100,046
--------END ITERATION REPORT--------


Saving checkpoint 112130604...
Checkpoint 112130604 saved!
