Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,716.99240
Policy Entropy: 4.01711
Value Function Loss: 0.54605

Mean KL Divergence: 0.00106
SB3 Clip Fraction: 0.05145
Policy Update Magnitude: 0.17264
Value Function Update Magnitude: 0.24202

Collected Steps per Second: 9,456.74436
Overall Steps per Second: 5,621.22159

Timestep Collection Time: 5.28829
Timestep Consumption Time: 3.60835
PPO Batch Consumption Time: 0.71170
Total Iteration Time: 8.89664

Cumulative Model Updates: 55,107
Cumulative Timesteps: 306,724,452

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,528.56699
Policy Entropy: 4.02173
Value Function Loss: 0.29643

Mean KL Divergence: 0.00332
SB3 Clip Fraction: 0.08002
Policy Update Magnitude: 0.20565
Value Function Update Magnitude: 0.19742

Collected Steps per Second: 10,148.82982
Overall Steps per Second: 7,549.12091

Timestep Collection Time: 4.92766
Timestep Consumption Time: 1.69695
PPO Batch Consumption Time: 0.25618
Total Iteration Time: 6.62461

Cumulative Model Updates: 55,110
Cumulative Timesteps: 306,774,462

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,708.72113
Policy Entropy: 4.01954
Value Function Loss: 0.19261

Mean KL Divergence: 0.00603
SB3 Clip Fraction: 0.15667
Policy Update Magnitude: 0.32152
Value Function Update Magnitude: 0.31455

Collected Steps per Second: 10,039.10876
Overall Steps per Second: 6,659.53972

Timestep Collection Time: 4.98152
Timestep Consumption Time: 2.52801
PPO Batch Consumption Time: 0.26046
Total Iteration Time: 7.50953

Cumulative Model Updates: 55,116
Cumulative Timesteps: 306,824,472

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,434.77695
Policy Entropy: 4.03434
Value Function Loss: 0.03901

Mean KL Divergence: 0.00254
SB3 Clip Fraction: 0.12614
Policy Update Magnitude: 0.34443
Value Function Update Magnitude: 0.32886

Collected Steps per Second: 10,421.45648
Overall Steps per Second: 6,155.41080

Timestep Collection Time: 4.79990
Timestep Consumption Time: 3.32660
PPO Batch Consumption Time: 0.25899
Total Iteration Time: 8.12651

Cumulative Model Updates: 55,125
Cumulative Timesteps: 306,874,494

Timesteps Collected: 50,022
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,693.56897
Policy Entropy: 4.03612
Value Function Loss: 0.03494

Mean KL Divergence: 0.00210
SB3 Clip Fraction: 0.09979
Policy Update Magnitude: 0.28938
Value Function Update Magnitude: 0.30702

Collected Steps per Second: 9,838.70924
Overall Steps per Second: 5,916.28933

Timestep Collection Time: 5.08522
Timestep Consumption Time: 3.37143
PPO Batch Consumption Time: 0.26489
Total Iteration Time: 8.45665

Cumulative Model Updates: 55,134
Cumulative Timesteps: 306,924,526

Timesteps Collected: 50,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,436.44999
Policy Entropy: 4.03222
Value Function Loss: 0.03351

Mean KL Divergence: 0.00158
SB3 Clip Fraction: 0.06994
Policy Update Magnitude: 0.29012
Value Function Update Magnitude: 0.35307

Collected Steps per Second: 10,302.57188
Overall Steps per Second: 6,112.70547

Timestep Collection Time: 4.85529
Timestep Consumption Time: 3.32799
PPO Batch Consumption Time: 0.26384
Total Iteration Time: 8.18328

Cumulative Model Updates: 55,143
Cumulative Timesteps: 306,974,548

Timesteps Collected: 50,022
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,167.68955
Policy Entropy: 4.02711
Value Function Loss: 0.03288

Mean KL Divergence: 0.00148
SB3 Clip Fraction: 0.06645
Policy Update Magnitude: 0.29134
Value Function Update Magnitude: 0.40926

Collected Steps per Second: 10,100.51939
Overall Steps per Second: 6,061.21476

Timestep Collection Time: 4.95262
Timestep Consumption Time: 3.30051
PPO Batch Consumption Time: 0.26077
Total Iteration Time: 8.25313

Cumulative Model Updates: 55,152
Cumulative Timesteps: 307,024,572

Timesteps Collected: 50,024
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,893.60919
Policy Entropy: 4.02604
Value Function Loss: 0.03332

Mean KL Divergence: 0.00154
SB3 Clip Fraction: 0.06850
Policy Update Magnitude: 0.28816
Value Function Update Magnitude: 0.41050

Collected Steps per Second: 9,059.59686
Overall Steps per Second: 5,627.03083

Timestep Collection Time: 5.52276
Timestep Consumption Time: 3.36896
PPO Batch Consumption Time: 0.26479
Total Iteration Time: 8.89172

Cumulative Model Updates: 55,161
Cumulative Timesteps: 307,074,606

Timesteps Collected: 50,034
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,601.14697
Policy Entropy: 4.02771
Value Function Loss: 0.03458

Mean KL Divergence: 0.00161
SB3 Clip Fraction: 0.07170
Policy Update Magnitude: 0.29544
Value Function Update Magnitude: 0.48521

Collected Steps per Second: 10,705.26506
Overall Steps per Second: 6,306.08680

Timestep Collection Time: 4.67471
Timestep Consumption Time: 3.26112
PPO Batch Consumption Time: 0.25819
Total Iteration Time: 7.93582

Cumulative Model Updates: 55,170
Cumulative Timesteps: 307,124,650

Timesteps Collected: 50,044
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,608.66610
Policy Entropy: 4.03376
Value Function Loss: 0.03763

Mean KL Divergence: 0.00159
SB3 Clip Fraction: 0.07028
Policy Update Magnitude: 0.30084
Value Function Update Magnitude: 0.49224

Collected Steps per Second: 9,539.70949
Overall Steps per Second: 5,894.61274

Timestep Collection Time: 5.24230
Timestep Consumption Time: 3.24172
PPO Batch Consumption Time: 0.25403
Total Iteration Time: 8.48402

Cumulative Model Updates: 55,179
Cumulative Timesteps: 307,174,660

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,052.90071
Policy Entropy: 4.03783
Value Function Loss: 0.04037

Mean KL Divergence: 0.00160
SB3 Clip Fraction: 0.07092
Policy Update Magnitude: 0.30591
Value Function Update Magnitude: 0.42974

Collected Steps per Second: 9,963.44790
Overall Steps per Second: 6,046.56637

Timestep Collection Time: 5.01895
Timestep Consumption Time: 3.25120
PPO Batch Consumption Time: 0.25612
Total Iteration Time: 8.27015

Cumulative Model Updates: 55,188
Cumulative Timesteps: 307,224,666

Timesteps Collected: 50,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,850.04069
Policy Entropy: 4.03567
Value Function Loss: 0.04230

Mean KL Divergence: 0.00165
SB3 Clip Fraction: 0.07572
Policy Update Magnitude: 0.31164
Value Function Update Magnitude: 0.54953

Collected Steps per Second: 9,909.15237
Overall Steps per Second: 5,987.48398

Timestep Collection Time: 5.04968
Timestep Consumption Time: 3.30742
PPO Batch Consumption Time: 0.25713
Total Iteration Time: 8.35710

Cumulative Model Updates: 55,197
Cumulative Timesteps: 307,274,704

Timesteps Collected: 50,038
--------END ITERATION REPORT--------


Saving checkpoint 307274704...
Checkpoint 307274704 saved!
