Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 414.55235
Policy Entropy: 4.00819
Value Function Loss: 0.04096

Mean KL Divergence: 0.00024
SB3 Clip Fraction: 0.00573
Policy Update Magnitude: 0.07275
Value Function Update Magnitude: 0.13719

Collected Steps per Second: 11,031.46391
Overall Steps per Second: 6,351.31753

Timestep Collection Time: 4.53829
Timestep Consumption Time: 3.34417
PPO Batch Consumption Time: 0.66268
Total Iteration Time: 7.88246

Cumulative Model Updates: 11,718
Cumulative Timesteps: 65,397,956

Timesteps Collected: 50,064
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 758.97984
Policy Entropy: 4.01954
Value Function Loss: 0.03315

Mean KL Divergence: 0.00106
SB3 Clip Fraction: 0.04569
Policy Update Magnitude: 0.17062
Value Function Update Magnitude: 0.32893

Collected Steps per Second: 14,014.40569
Overall Steps per Second: 8,392.90981

Timestep Collection Time: 3.56876
Timestep Consumption Time: 2.39032
PPO Batch Consumption Time: 0.26108
Total Iteration Time: 5.95908

Cumulative Model Updates: 11,724
Cumulative Timesteps: 65,447,970

Timesteps Collected: 50,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 931.14656
Policy Entropy: 4.02708
Value Function Loss: 0.02835

Mean KL Divergence: 0.00151
SB3 Clip Fraction: 0.07268
Policy Update Magnitude: 0.24197
Value Function Update Magnitude: 0.46959

Collected Steps per Second: 13,969.80714
Overall Steps per Second: 7,329.43837

Timestep Collection Time: 3.58115
Timestep Consumption Time: 3.24447
PPO Batch Consumption Time: 0.26033
Total Iteration Time: 6.82563

Cumulative Model Updates: 11,733
Cumulative Timesteps: 65,497,998

Timesteps Collected: 50,028
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,560.04505
Policy Entropy: 4.04064
Value Function Loss: 0.02731

Mean KL Divergence: 0.00125
SB3 Clip Fraction: 0.05636
Policy Update Magnitude: 0.21904
Value Function Update Magnitude: 0.40977

Collected Steps per Second: 13,955.86925
Overall Steps per Second: 7,377.96689

Timestep Collection Time: 3.58731
Timestep Consumption Time: 3.19830
PPO Batch Consumption Time: 0.26045
Total Iteration Time: 6.78561

Cumulative Model Updates: 11,742
Cumulative Timesteps: 65,548,062

Timesteps Collected: 50,064
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,325.74796
Policy Entropy: 4.04131
Value Function Loss: 0.02587

Mean KL Divergence: 0.00122
SB3 Clip Fraction: 0.05316
Policy Update Magnitude: 0.20793
Value Function Update Magnitude: 0.39874

Collected Steps per Second: 11,524.35075
Overall Steps per Second: 6,630.43439

Timestep Collection Time: 4.34280
Timestep Consumption Time: 3.20542
PPO Batch Consumption Time: 0.26103
Total Iteration Time: 7.54822

Cumulative Model Updates: 11,751
Cumulative Timesteps: 65,598,110

Timesteps Collected: 50,048
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,510.46029
Policy Entropy: 4.04004
Value Function Loss: 0.02845

Mean KL Divergence: 0.00120
SB3 Clip Fraction: 0.05223
Policy Update Magnitude: 0.20433
Value Function Update Magnitude: 0.38723

Collected Steps per Second: 13,984.09162
Overall Steps per Second: 7,369.42735

Timestep Collection Time: 3.57878
Timestep Consumption Time: 3.21225
PPO Batch Consumption Time: 0.26359
Total Iteration Time: 6.79103

Cumulative Model Updates: 11,760
Cumulative Timesteps: 65,648,156

Timesteps Collected: 50,046
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,595.75588
Policy Entropy: 4.04095
Value Function Loss: 0.02759

Mean KL Divergence: 0.00113
SB3 Clip Fraction: 0.04755
Policy Update Magnitude: 0.20541
Value Function Update Magnitude: 0.39218

Collected Steps per Second: 11,743.01722
Overall Steps per Second: 6,654.80346

Timestep Collection Time: 4.26517
Timestep Consumption Time: 3.26112
PPO Batch Consumption Time: 0.26487
Total Iteration Time: 7.52629

Cumulative Model Updates: 11,769
Cumulative Timesteps: 65,698,242

Timesteps Collected: 50,086
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,733.13554
Policy Entropy: 4.04463
Value Function Loss: 0.02819

Mean KL Divergence: 0.00112
SB3 Clip Fraction: 0.04749
Policy Update Magnitude: 0.20677
Value Function Update Magnitude: 0.39121

Collected Steps per Second: 12,677.62472
Overall Steps per Second: 6,923.58491

Timestep Collection Time: 3.94459
Timestep Consumption Time: 3.27826
PPO Batch Consumption Time: 0.26673
Total Iteration Time: 7.22285

Cumulative Model Updates: 11,778
Cumulative Timesteps: 65,748,250

Timesteps Collected: 50,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,050.39985
Policy Entropy: 4.04357
Value Function Loss: 0.02678

Mean KL Divergence: 0.00114
SB3 Clip Fraction: 0.04756
Policy Update Magnitude: 0.20271
Value Function Update Magnitude: 0.38786

Collected Steps per Second: 12,519.34384
Overall Steps per Second: 6,869.84087

Timestep Collection Time: 3.99462
Timestep Consumption Time: 3.28503
PPO Batch Consumption Time: 0.26567
Total Iteration Time: 7.27964

Cumulative Model Updates: 11,787
Cumulative Timesteps: 65,798,260

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,179.52008
Policy Entropy: 4.04223
Value Function Loss: 0.02636

Mean KL Divergence: 0.00115
SB3 Clip Fraction: 0.04852
Policy Update Magnitude: 0.20656
Value Function Update Magnitude: 0.36748

Collected Steps per Second: 12,352.14910
Overall Steps per Second: 6,848.53790

Timestep Collection Time: 4.04788
Timestep Consumption Time: 3.25295
PPO Batch Consumption Time: 0.26554
Total Iteration Time: 7.30083

Cumulative Model Updates: 11,796
Cumulative Timesteps: 65,848,260

Timesteps Collected: 50,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,780.08300
Policy Entropy: 4.04028
Value Function Loss: 0.02795

Mean KL Divergence: 0.00119
SB3 Clip Fraction: 0.05201
Policy Update Magnitude: 0.20912
Value Function Update Magnitude: 0.38057

Collected Steps per Second: 10,110.79931
Overall Steps per Second: 6,079.24052

Timestep Collection Time: 4.95371
Timestep Consumption Time: 3.28514
PPO Batch Consumption Time: 0.26460
Total Iteration Time: 8.23886

Cumulative Model Updates: 11,805
Cumulative Timesteps: 65,898,346

Timesteps Collected: 50,086
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,741.54060
Policy Entropy: 4.03955
Value Function Loss: 0.02884

Mean KL Divergence: 0.00120
SB3 Clip Fraction: 0.05371
Policy Update Magnitude: 0.21377
Value Function Update Magnitude: 0.40621

Collected Steps per Second: 10,659.49416
Overall Steps per Second: 6,252.79812

Timestep Collection Time: 4.69853
Timestep Consumption Time: 3.31132
PPO Batch Consumption Time: 0.26871
Total Iteration Time: 8.00985

Cumulative Model Updates: 11,814
Cumulative Timesteps: 65,948,430

Timesteps Collected: 50,084
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,016.93045
Policy Entropy: 4.03837
Value Function Loss: 0.03047

Mean KL Divergence: 0.00136
SB3 Clip Fraction: 0.06245
Policy Update Magnitude: 0.21513
Value Function Update Magnitude: 0.43466

Collected Steps per Second: 11,878.37988
Overall Steps per Second: 6,666.95942

Timestep Collection Time: 4.21152
Timestep Consumption Time: 3.29205
PPO Batch Consumption Time: 0.26729
Total Iteration Time: 7.50357

Cumulative Model Updates: 11,823
Cumulative Timesteps: 65,998,456

Timesteps Collected: 50,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,163.20402
Policy Entropy: 4.03849
Value Function Loss: 0.03146

Mean KL Divergence: 0.00118
SB3 Clip Fraction: 0.04961
Policy Update Magnitude: 0.21851
Value Function Update Magnitude: 0.45201

Collected Steps per Second: 12,806.18808
Overall Steps per Second: 6,937.46950

Timestep Collection Time: 3.90717
Timestep Consumption Time: 3.30525
PPO Batch Consumption Time: 0.26771
Total Iteration Time: 7.21243

Cumulative Model Updates: 11,832
Cumulative Timesteps: 66,048,492

Timesteps Collected: 50,036
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,642.84077
Policy Entropy: 4.03775
Value Function Loss: 0.03240

Mean KL Divergence: 0.00123
SB3 Clip Fraction: 0.05635
Policy Update Magnitude: 0.22006
Value Function Update Magnitude: 0.46667

Collected Steps per Second: 12,448.73244
Overall Steps per Second: 6,841.30875

Timestep Collection Time: 4.01679
Timestep Consumption Time: 3.29233
PPO Batch Consumption Time: 0.26618
Total Iteration Time: 7.30913

Cumulative Model Updates: 11,841
Cumulative Timesteps: 66,098,496

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,942.03942
Policy Entropy: 4.03799
Value Function Loss: 0.02911

Mean KL Divergence: 0.00114
SB3 Clip Fraction: 0.04951
Policy Update Magnitude: 0.21902
Value Function Update Magnitude: 0.44447

Collected Steps per Second: 11,021.16263
Overall Steps per Second: 6,282.63704

Timestep Collection Time: 4.54054
Timestep Consumption Time: 3.42459
PPO Batch Consumption Time: 0.27636
Total Iteration Time: 7.96513

Cumulative Model Updates: 11,850
Cumulative Timesteps: 66,148,538

Timesteps Collected: 50,042
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,005.54802
Policy Entropy: 4.03759
Value Function Loss: 0.02722

Mean KL Divergence: 0.00108
SB3 Clip Fraction: 0.04432
Policy Update Magnitude: 0.21607
Value Function Update Magnitude: 0.40054

Collected Steps per Second: 10,930.87293
Overall Steps per Second: 6,210.80305

Timestep Collection Time: 4.57475
Timestep Consumption Time: 3.47671
PPO Batch Consumption Time: 0.27522
Total Iteration Time: 8.05145

Cumulative Model Updates: 11,859
Cumulative Timesteps: 66,198,544

Timesteps Collected: 50,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,174.40883
Policy Entropy: 4.03811
Value Function Loss: 0.02808

Mean KL Divergence: 0.00114
SB3 Clip Fraction: 0.04735
Policy Update Magnitude: 0.21479
Value Function Update Magnitude: 0.39337

Collected Steps per Second: 10,866.23637
Overall Steps per Second: 6,219.98608

Timestep Collection Time: 4.60141
Timestep Consumption Time: 3.43719
PPO Batch Consumption Time: 0.27591
Total Iteration Time: 8.03860

Cumulative Model Updates: 11,868
Cumulative Timesteps: 66,248,544

Timesteps Collected: 50,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,689.55359
Policy Entropy: 4.03785
Value Function Loss: 0.02764

Mean KL Divergence: 0.00123
SB3 Clip Fraction: 0.05623
Policy Update Magnitude: 0.21574
Value Function Update Magnitude: 0.40443

Collected Steps per Second: 11,166.70352
Overall Steps per Second: 6,318.29908

Timestep Collection Time: 4.47939
Timestep Consumption Time: 3.43730
PPO Batch Consumption Time: 0.27686
Total Iteration Time: 7.91669

Cumulative Model Updates: 11,877
Cumulative Timesteps: 66,298,564

Timesteps Collected: 50,020
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,608.66127
Policy Entropy: 4.03822
Value Function Loss: 0.02949

Mean KL Divergence: 0.00108
SB3 Clip Fraction: 0.04534
Policy Update Magnitude: 0.21409
Value Function Update Magnitude: 0.40921

Collected Steps per Second: 10,867.52853
Overall Steps per Second: 6,283.29711

Timestep Collection Time: 4.60252
Timestep Consumption Time: 3.35795
PPO Batch Consumption Time: 0.26787
Total Iteration Time: 7.96047

Cumulative Model Updates: 11,886
Cumulative Timesteps: 66,348,582

Timesteps Collected: 50,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,886.12120
Policy Entropy: 4.03792
Value Function Loss: 0.02775

Mean KL Divergence: 0.00106
SB3 Clip Fraction: 0.04297
Policy Update Magnitude: 0.21362
Value Function Update Magnitude: 0.40660

Collected Steps per Second: 12,763.26434
Overall Steps per Second: 6,974.01484

Timestep Collection Time: 3.91828
Timestep Consumption Time: 3.25263
PPO Batch Consumption Time: 0.26613
Total Iteration Time: 7.17091

Cumulative Model Updates: 11,895
Cumulative Timesteps: 66,398,592

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,656.74193
Policy Entropy: 4.04214
Value Function Loss: 0.02847

Mean KL Divergence: 0.00117
SB3 Clip Fraction: 0.05198
Policy Update Magnitude: 0.21040
Value Function Update Magnitude: 0.42411

Collected Steps per Second: 12,654.24959
Overall Steps per Second: 6,914.59805

Timestep Collection Time: 3.95377
Timestep Consumption Time: 3.28194
PPO Batch Consumption Time: 0.26712
Total Iteration Time: 7.23571

Cumulative Model Updates: 11,904
Cumulative Timesteps: 66,448,624

Timesteps Collected: 50,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,388.57321
Policy Entropy: 4.04191
Value Function Loss: 0.02630

Mean KL Divergence: 0.00119
SB3 Clip Fraction: 0.05128
Policy Update Magnitude: 0.21121
Value Function Update Magnitude: 0.40695

Collected Steps per Second: 12,349.60827
Overall Steps per Second: 6,743.39803

Timestep Collection Time: 4.05065
Timestep Consumption Time: 3.36756
PPO Batch Consumption Time: 0.26836
Total Iteration Time: 7.41822

Cumulative Model Updates: 11,913
Cumulative Timesteps: 66,498,648

Timesteps Collected: 50,024
--------END ITERATION REPORT--------


Saving checkpoint 66498648...
Checkpoint 66498648 saved!
