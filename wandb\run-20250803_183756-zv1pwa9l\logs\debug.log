2025-08-03 18:37:56,477 INFO    MainThread:20996 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_setup.py:_flush():80] Configure stats pid to 20996
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250803_183756-zv1pwa9l\logs\debug.log
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250803_183756-zv1pwa9l\logs\debug-internal.log
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_init.py:init():830] calling init triggers
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 100000000000, 'exp_buffer_size': 150000, 'ts_per_iteration': 50000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (2048, 2048, 1024, 1024), 'critic_layer_sizes': (2048, 2048, 1024, 1024), 'ppo_epochs': 3, 'ppo_batch_size': 50000, 'ppo_minibatch_size': 25000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-08-03 18:37:56,478 INFO    MainThread:20996 [wandb_init.py:init():871] starting backend
2025-08-03 18:37:56,692 INFO    MainThread:20996 [wandb_init.py:init():874] sending inform_init request
2025-08-03 18:37:56,703 INFO    MainThread:20996 [wandb_init.py:init():882] backend started and connected
2025-08-03 18:37:56,704 INFO    MainThread:20996 [wandb_init.py:init():953] updated telemetry
2025-08-03 18:37:56,706 INFO    MainThread:20996 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-03 18:37:57,388 INFO    MainThread:20996 [wandb_init.py:init():1029] starting run threads in backend
2025-08-03 18:37:57,452 INFO    MainThread:20996 [wandb_run.py:_console_start():2458] atexit reg
2025-08-03 18:37:57,453 INFO    MainThread:20996 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-03 18:37:57,453 INFO    MainThread:20996 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-03 18:37:57,453 INFO    MainThread:20996 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-03 18:37:57,455 INFO    MainThread:20996 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-03 18:42:25,875 WARNING MsgRouterThr:20996 [router.py:message_loop():63] [no run ID] message_loop has been closed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 204, in _read_packet_bytes
    data = self._sock.recv(self._bufsize)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\router_sock.py", line 27, in _read_message
    return self._sock_client.read_server_response(timeout=1)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 231, in read_server_response
    data = self._read_packet_bytes(timeout=timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 208, in _read_packet_bytes
    raise SockClientClosedError from e
wandb.sdk.lib.sock_client.SockClientClosedError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\router.py", line 56, in message_loop
    msg = self._read_message()
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\router_sock.py", line 29, in _read_message
    raise MessageRouterClosedError from e
wandb.sdk.interface.router.MessageRouterClosedError
2025-08-03 18:42:25,876 INFO    MsgRouterThr:20996 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
2025-08-03 18:42:28,087 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,094 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,098 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,102 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,105 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,108 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,109 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,112 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,114 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,116 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,117 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,120 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,124 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,125 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,126 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,130 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,133 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,135 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,136 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,139 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,142 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,144 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,147 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,149 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,151 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,153 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,155 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,157 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,160 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,162 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,165 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,168 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,170 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,172 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,175 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,178 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,181 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,183 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,185 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,186 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,188 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-08-03 18:42:28,191 ERROR   MainThread:20996 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 294, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
           ^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
