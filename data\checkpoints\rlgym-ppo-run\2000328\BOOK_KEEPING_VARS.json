{"cumulative_timesteps": 2000328, "cumulative_model_updates": 55, "policy_average_reward": 85.95442199707031, "epoch": 20, "ts_since_last_save": 2000328, "reward_running_stats": {"mean": [0.13772791624069214], "var": [912852.375], "shape": [1], "count": 3000}, "wandb_run_id": "xg314ml2", "wandb_project": "rlgym-ppo", "wandb_entity": "xzskullyxz-rlgym", "wandb_group": "unnamed-runs", "wandb_config": {"n_proc": 32, "min_inference_size": 29, "timestep_limit": 100000000000, "exp_buffer_size": 300000, "ts_per_iteration": 100000, "standardize_returns": true, "standardize_obs": false, "policy_layer_sizes": [512, 512, 512, 512], "critic_layer_sizes": [512, 512, 512, 512], "ppo_epochs": 1, "ppo_batch_size": 100000, "ppo_minibatch_size": 50000, "ppo_ent_coef": 0.01, "ppo_clip_range": 0.1, "gae_lambda": 0.95, "gae_gamma": 0.99, "policy_lr": 0.0002, "critic_lr": 0.0002, "shm_buffer_size": 8192}}