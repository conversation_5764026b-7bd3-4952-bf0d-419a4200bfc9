Created new wandb run! qfioqv7n
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 81.23678
Policy Entropy: 4.49940
Value Function Loss: nan

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.17724
Value Function Update Magnitude: 0.16685

Collected Steps per Second: 10,380.35587
Overall Steps per Second: 7,596.61363

Timestep Collection Time: 9.63416
Timestep Consumption Time: 3.53039
PPO Batch Consumption Time: 1.27135
Total Iteration Time: 13.16455

Cumulative Model Updates: 1
Cumulative Timesteps: 100,006

Timesteps Collected: 100,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 41.46087
Policy Entropy: 4.49936
Value Function Loss: 241.36963

Mean KL Divergence: 0.00001
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.14166
Value Function Update Magnitude: 0.15200

Collected Steps per Second: 11,008.16499
Overall Steps per Second: 8,921.32478

Timestep Collection Time: 9.08598
Timestep Consumption Time: 2.12536
PPO Batch Consumption Time: 0.44563
Total Iteration Time: 11.21134

Cumulative Model Updates: 2
Cumulative Timesteps: 200,026

Timesteps Collected: 100,020
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 55.25218
Policy Entropy: 4.49928
Value Function Loss: 160.66388

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.31456
Value Function Update Magnitude: 0.40416

Collected Steps per Second: 10,998.28470
Overall Steps per Second: 8,717.70953

Timestep Collection Time: 9.09578
Timestep Consumption Time: 2.37948
PPO Batch Consumption Time: 0.27699
Total Iteration Time: 11.47526

Cumulative Model Updates: 5
Cumulative Timesteps: 300,064

Timesteps Collected: 100,038
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 41.67673
Policy Entropy: 4.49904
Value Function Loss: 0.06288

Mean KL Divergence: 0.00010
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.22098
Value Function Update Magnitude: 0.28938

Collected Steps per Second: 11,764.48416
Overall Steps per Second: 9,135.03254

Timestep Collection Time: 8.50016
Timestep Consumption Time: 2.44671
PPO Batch Consumption Time: 0.27998
Total Iteration Time: 10.94687

Cumulative Model Updates: 8
Cumulative Timesteps: 400,064

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 112.71257
Policy Entropy: 4.49873
Value Function Loss: 0.07422

Mean KL Divergence: 0.00013
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.15430
Value Function Update Magnitude: 0.28588

Collected Steps per Second: 11,660.29562
Overall Steps per Second: 9,164.78042

Timestep Collection Time: 8.57766
Timestep Consumption Time: 2.33564
PPO Batch Consumption Time: 0.27381
Total Iteration Time: 10.91330

Cumulative Model Updates: 11
Cumulative Timesteps: 500,082

Timesteps Collected: 100,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 56.32719
Policy Entropy: 4.49842
Value Function Loss: 0.09213

Mean KL Divergence: 0.00009
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.11355
Value Function Update Magnitude: 0.24644

Collected Steps per Second: 11,443.48891
Overall Steps per Second: 9,038.04974

Timestep Collection Time: 8.74104
Timestep Consumption Time: 2.32639
PPO Batch Consumption Time: 0.27547
Total Iteration Time: 11.06743

Cumulative Model Updates: 14
Cumulative Timesteps: 600,110

Timesteps Collected: 100,028
--------END ITERATION REPORT--------
