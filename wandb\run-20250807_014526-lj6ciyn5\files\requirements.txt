annotated-types==0.7.0
attrs==25.3.0
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
cmeel==0.57.3
colorama==0.4.6
contourpy==1.3.3
customtkinter==5.2.0
cycler==0.12.1
darkdetect==0.8.0
dataclasses==0.6
docopt==0.6.2
Farama-Notifications==0.0.4
filelock==3.18.0
flatbuffers==1.12
fonttools==4.59.0
fsspec==2025.5.1
gitdb==4.0.12
GitPython==3.1.44
glcontext==3.0.0
gym==0.26.2
gymnasium==1.2.0
gym-notices==0.0.8
h11==0.16.0
idna==3.10
inputs==0.5
Jinja2==3.1.6
keyboard==0.13.5
kiwisolver==1.4.8
llvmlite==0.44.0
MarkupSafe==3.0.2
matplotlib==3.10.5
moderngl==5.12.0
moderngl-window==3.1.1
mpmath==1.3.0
multipledispatch==1.0.0
networkx==3.5
numba==0.61.2
numpy==1.26.4
outcome==1.3.0.post0
packaging==25.0
pandas==2.3.1
pillow==11.3.0
pip==25.1.1
platformdirs==4.3.8
protobuf==6.31.1
psutil==5.9.6
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
pyglet==2.1.6
pyglm==2.8.2
Pymem==1.13.0
pynput==1.7.6
PyOpenGL==3.1.9
pyparsing==3.2.3
PyQt5==5.15.11
PyQt5-Qt5==5.15.2
PyQt5_sip==12.17.0
pyrr==0.10.3
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytz==2025.2
PyWavefront==1.3.3
pywin32==306
PyYAML==6.0.2
requests==2.32.4
requirements-parser==0.13.0
rlbot==1.68.0
rlgym==2.0.1
rlgym-api==2.0.0
rlgym-compat==1.1.0
rlgym-ppo==1.3.13
rlgym-rocket-league==2.0.1
rlgym-sim==1.2.6
rlgym-tools==2.3.9
rlviser-py==0.6.10
rocketsim==2.1.1.post4
selenium==4.34.2
sentry-sdk==2.32.0
setuptools==65.5.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sortedcontainers==2.4.0
stable_baselines3==2.7.0
sympy==1.14.0
torch==2.4.1
torchaudio==2.1.2+cpu
torchvision==0.19.1
torch-directml==0.2.5.dev240914
trio==0.30.0
trio-websocket==0.12.2
typing_extensions==4.14.1
typing-inspection==0.4.1
tzdata==2025.2
urllib3==2.5.0
wandb==0.21.0
webdriver-manager==4.0.2
websockets==15.0.1
websocket-client==1.8.0
wsproto==1.2.0
