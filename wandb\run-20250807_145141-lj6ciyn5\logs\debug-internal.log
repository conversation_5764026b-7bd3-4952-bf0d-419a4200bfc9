{"time":"2025-08-07T14:51:41.3840959-05:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T14:51:41.8054446-05:00","level":"INFO","msg":"stream: created new stream","id":"lj6ciyn5"}
{"time":"2025-08-07T14:51:41.8054446-05:00","level":"INFO","msg":"stream: started","id":"lj6ciyn5"}
{"time":"2025-08-07T14:51:41.8059599-05:00","level":"INFO","msg":"handler: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T14:51:41.8059599-05:00","level":"INFO","msg":"sender: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T14:51:41.8059599-05:00","level":"INFO","msg":"writer: Do: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T14:52:12.0833592-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T14:53:41.3705207-05:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-07T14:53:41.5965694-05:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-07T14:53:41.6002507-05:00","level":"INFO","msg":"stream: closing","id":"lj6ciyn5"}
{"time":"2025-08-07T14:53:41.6002507-05:00","level":"INFO","msg":"sender: closed","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T14:53:41.6002507-05:00","level":"INFO","msg":"handler: closed","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T14:53:41.6002507-05:00","level":"INFO","msg":"writer: Close: closed","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T14:53:41.6007541-05:00","level":"INFO","msg":"stream: closed","id":"lj6ciyn5"}
