Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 332.44505
Policy Entropy: 4.07344
Value Function Loss: 0.03103

Mean KL Divergence: 0.00052
SB3 Clip Fraction: 0.02315
Policy Update Magnitude: 0.05396
Value Function Update Magnitude: 0.09704

Collected Steps per Second: 8,697.60932
Overall Steps per Second: 5,712.34316

Timestep Collection Time: 11.49764
Timestep Consumption Time: 6.00866
PPO Batch Consumption Time: 1.09063
Total Iteration Time: 17.50630

Cumulative Model Updates: 6,648
Cumulative Timesteps: 74,725,308

Timesteps Collected: 100,002
--------END ITERATION REPORT--------


Saving checkpoint 74725308...
Checkpoint 74725308 saved!
