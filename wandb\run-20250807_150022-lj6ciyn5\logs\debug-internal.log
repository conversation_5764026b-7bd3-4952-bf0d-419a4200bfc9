{"time":"2025-08-07T15:00:23.0126673-05:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-07T15:00:23.2880406-05:00","level":"INFO","msg":"stream: created new stream","id":"lj6ciyn5"}
{"time":"2025-08-07T15:00:23.2880406-05:00","level":"INFO","msg":"stream: started","id":"lj6ciyn5"}
{"time":"2025-08-07T15:00:23.2880406-05:00","level":"INFO","msg":"handler: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T15:00:23.2880406-05:00","level":"INFO","msg":"sender: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T15:00:23.2880406-05:00","level":"INFO","msg":"writer: Do: started","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T15:21:54.9725835-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T16:10:12.4024979-05:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/xzskullyxz-rlgym/rlgym-ppo/lj6ciyn5/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-07T17:08:14.977387-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-07T19:36:03.0622843-05:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-07T19:36:03.2562785-05:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-07T19:36:03.2597755-05:00","level":"INFO","msg":"stream: closing","id":"lj6ciyn5"}
{"time":"2025-08-07T19:36:03.2597755-05:00","level":"INFO","msg":"handler: closed","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T19:36:03.2597755-05:00","level":"INFO","msg":"sender: closed","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T19:36:03.2597755-05:00","level":"INFO","msg":"writer: Close: closed","stream_id":"lj6ciyn5"}
{"time":"2025-08-07T19:36:03.2597755-05:00","level":"INFO","msg":"stream: closed","id":"lj6ciyn5"}
