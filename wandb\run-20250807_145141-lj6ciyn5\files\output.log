Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 583.50746
Policy Entropy: 4.07011
Value Function Loss: 0.02990

Mean KL Divergence: 0.00038
SB3 Clip Fraction: 0.00845
Policy Update Magnitude: 0.05660
Value Function Update Magnitude: 0.08841

Collected Steps per Second: 12,643.34883
Overall Steps per Second: 7,073.46002

Timestep Collection Time: 7.91294
Timestep Consumption Time: 6.23092
PPO Batch Consumption Time: 1.14239
Total Iteration Time: 14.14386

Cumulative Model Updates: 6,603
Cumulative Timesteps: 74,125,074

Timesteps Collected: 100,046
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,385.84687
Policy Entropy: 4.07372
Value Function Loss: 0.02510

Mean KL Divergence: 0.00144
SB3 Clip Fraction: 0.06912
Policy Update Magnitude: 0.11150
Value Function Update Magnitude: 0.20776

Collected Steps per Second: 13,453.26411
Overall Steps per Second: 8,304.21665

Timestep Collection Time: 7.43805
Timestep Consumption Time: 4.61198
PPO Batch Consumption Time: 0.46800
Total Iteration Time: 12.05002

Cumulative Model Updates: 6,609
Cumulative Timesteps: 74,225,140

Timesteps Collected: 100,066
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,104.40356
Policy Entropy: 4.07776
Value Function Loss: 0.02388

Mean KL Divergence: 0.00166
SB3 Clip Fraction: 0.08265
Policy Update Magnitude: 0.15484
Value Function Update Magnitude: 0.30021

Collected Steps per Second: 13,316.14270
Overall Steps per Second: 7,261.64233

Timestep Collection Time: 7.50998
Timestep Consumption Time: 6.26156
PPO Batch Consumption Time: 0.48837
Total Iteration Time: 13.77154

Cumulative Model Updates: 6,618
Cumulative Timesteps: 74,325,144

Timesteps Collected: 100,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 790.26533
Policy Entropy: 4.08204
Value Function Loss: 0.02231

Mean KL Divergence: 0.00135
SB3 Clip Fraction: 0.06043
Policy Update Magnitude: 0.14613
Value Function Update Magnitude: 0.27599

Collected Steps per Second: 10,030.94004
Overall Steps per Second: 6,045.62699

Timestep Collection Time: 9.97853
Timestep Consumption Time: 6.57790
PPO Batch Consumption Time: 0.46984
Total Iteration Time: 16.55643

Cumulative Model Updates: 6,627
Cumulative Timesteps: 74,425,238

Timesteps Collected: 100,094
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,311.00967
Policy Entropy: 4.08038
Value Function Loss: 0.02278

Mean KL Divergence: 0.00126
SB3 Clip Fraction: 0.05458
Policy Update Magnitude: 0.14075
Value Function Update Magnitude: 0.27234

Collected Steps per Second: 13,528.39921
Overall Steps per Second: 7,444.20254

Timestep Collection Time: 7.39201
Timestep Consumption Time: 6.04154
PPO Batch Consumption Time: 0.46453
Total Iteration Time: 13.43354

Cumulative Model Updates: 6,636
Cumulative Timesteps: 74,525,240

Timesteps Collected: 100,002
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,466.00350
Policy Entropy: 4.08288
Value Function Loss: 0.02175

Mean KL Divergence: 0.00103
SB3 Clip Fraction: 0.04133
Policy Update Magnitude: 0.13820
Value Function Update Magnitude: 0.27162

Collected Steps per Second: 11,945.87594
Overall Steps per Second: 6,840.17279

Timestep Collection Time: 8.37661
Timestep Consumption Time: 6.25255
PPO Batch Consumption Time: 0.47393
Total Iteration Time: 14.62916

Cumulative Model Updates: 6,645
Cumulative Timesteps: 74,625,306

Timesteps Collected: 100,066
--------END ITERATION REPORT--------


Saving checkpoint 74625306...
Checkpoint 74625306 saved!
