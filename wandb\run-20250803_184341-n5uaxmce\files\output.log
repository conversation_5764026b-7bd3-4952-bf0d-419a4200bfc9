Created new wandb run! n5uax<PERSON><PERSON>
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 102.88188
Policy Entropy: 4.49944
Value Function Loss: nan

Mean KL Divergence: 0.00013
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.86508
Value Function Update Magnitude: 0.84869

Collected Steps per Second: 9,129.59351
Overall Steps per Second: 5,152.91878

Timestep Collection Time: 5.47735
Timestep Consumption Time: 4.22705
PPO Batch Consumption Time: 0.82754
Total Iteration Time: 9.70440

Cumulative Model Updates: 3
Cumulative Timesteps: 50,006

Timesteps Collected: 50,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: -94.11978
Policy Entropy: 4.49650
Value Function Loss: 1,687.22648

Mean KL Divergence: 0.00188
SB3 Clip Fraction: 0.11964
Policy Update Magnitude: 0.95915
Value Function Update Magnitude: 1.46456

Collected Steps per Second: 9,481.68549
Overall Steps per Second: 5,751.88724

Timestep Collection Time: 5.27564
Timestep Consumption Time: 3.42098
PPO Batch Consumption Time: 0.37600
Total Iteration Time: 8.69662

Cumulative Model Updates: 9
Cumulative Timesteps: 100,028

Timesteps Collected: 50,022
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 80.32205
Policy Entropy: 4.49655
Value Function Loss: 1,106.73305

Mean KL Divergence: 0.00139
SB3 Clip Fraction: 0.03581
Policy Update Magnitude: 0.69213
Value Function Update Magnitude: 1.48086

Collected Steps per Second: 10,201.34101
Overall Steps per Second: 5,997.13356

Timestep Collection Time: 4.90132
Timestep Consumption Time: 3.43600
PPO Batch Consumption Time: 0.37872
Total Iteration Time: 8.33732

Cumulative Model Updates: 15
Cumulative Timesteps: 150,028

Timesteps Collected: 50,000
--------END ITERATION REPORT--------


Saving checkpoint 150028...
Checkpoint 150028 saved!
