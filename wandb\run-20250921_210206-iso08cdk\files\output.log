Created new wandb run! iso08cdk
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 63.88914
Policy Entropy: 4.49940
Value Function Loss: nan

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.17748
Value Function Update Magnitude: 0.16757

Collected Steps per Second: 5,724.19490
Overall Steps per Second: 4,370.79032

Timestep Collection Time: 17.47320
Timestep Consumption Time: 5.41053
PPO Batch Consumption Time: 2.12365
Total Iteration Time: 22.88373

Cumulative Model Updates: 1
Cumulative Timesteps: 100,020

Timesteps Collected: 100,020
--------END ITERATION REPORT--------


Saving checkpoint 100020...
Checkpoint 100020 saved!
