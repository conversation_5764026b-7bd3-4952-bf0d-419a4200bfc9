Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 2,009.09692
Policy Entropy: 3.88200
Value Function Loss: 0.35475

Mean KL Divergence: 0.00053
SB3 Clip Fraction: 0.01779
Policy Update Magnitude: 0.12324
Value Function Update Magnitude: 0.15737

Collected Steps per Second: 10,953.38597
Overall Steps per Second: 6,443.87126

Timestep Collection Time: 9.13252
Timestep Consumption Time: 6.39107
PPO Batch Consumption Time: 1.13044
Total Iteration Time: 15.52359

Cumulative Model Updates: 19,530
Cumulative Timesteps: 111,529,752

Timesteps Collected: 100,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,281.91717
Policy Entropy: 3.88857
Value Function Loss: 0.32428

Mean KL Divergence: 0.00220
SB3 Clip Fraction: 0.11351
Policy Update Magnitude: 0.25156
Value Function Update Magnitude: 0.47001

Collected Steps per Second: 11,519.07071
Overall Steps per Second: 7,451.08747

Timestep Collection Time: 8.68369
Timestep Consumption Time: 4.74093
PPO Batch Consumption Time: 0.48748
Total Iteration Time: 13.42462

Cumulative Model Updates: 19,536
Cumulative Timesteps: 111,629,780

Timesteps Collected: 100,028
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,598.56412
Policy Entropy: 3.88859
Value Function Loss: 0.28960

Mean KL Divergence: 0.00201
SB3 Clip Fraction: 0.10193
Policy Update Magnitude: 0.24755
Value Function Update Magnitude: 0.51597

Collected Steps per Second: 10,875.01491
Overall Steps per Second: 7,133.04925

Timestep Collection Time: 9.20036
Timestep Consumption Time: 4.82647
PPO Batch Consumption Time: 0.49195
Total Iteration Time: 14.02682

Cumulative Model Updates: 19,542
Cumulative Timesteps: 111,729,834

Timesteps Collected: 100,054
--------END ITERATION REPORT--------


Saving checkpoint 111729834...
Checkpoint 111729834 saved!
