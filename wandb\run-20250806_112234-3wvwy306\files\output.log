Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,548.47465
Policy Entropy: 4.01842
Value Function Loss: 0.33603

Mean KL Divergence: 0.00081
SB3 Clip Fraction: 0.03447
Policy Update Magnitude: 0.10152
Value Function Update Magnitude: 0.11827

Collected Steps per Second: 11,224.88896
Overall Steps per Second: 6,597.00210

Timestep Collection Time: 8.90877
Timestep Consumption Time: 6.24963
PPO Batch Consumption Time: 1.09411
Total Iteration Time: 15.15840

Cumulative Model Updates: 10,863
Cumulative Timesteps: 60,694,466

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,940.53231
Policy Entropy: 4.02637
Value Function Loss: 0.22065

Mean KL Divergence: 0.00355
SB3 Clip Fraction: 0.16022
Policy Update Magnitude: 0.12740
Value Function Update Magnitude: 0.20536

Collected Steps per Second: 11,702.78697
Overall Steps per Second: 8,486.22560

Timestep Collection Time: 8.54959
Timestep Consumption Time: 3.24058
PPO Batch Consumption Time: 0.47426
Total Iteration Time: 11.79017

Cumulative Model Updates: 10,866
Cumulative Timesteps: 60,794,520

Timesteps Collected: 100,054
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,938.43022
Policy Entropy: 4.01953
Value Function Loss: 0.19224

Mean KL Divergence: 0.00417
SB3 Clip Fraction: 0.19506
Policy Update Magnitude: 0.21416
Value Function Update Magnitude: 0.35638

Collected Steps per Second: 11,918.10806
Overall Steps per Second: 7,637.78519

Timestep Collection Time: 8.39059
Timestep Consumption Time: 4.70221
PPO Batch Consumption Time: 0.47660
Total Iteration Time: 13.09280

Cumulative Model Updates: 10,872
Cumulative Timesteps: 60,894,520

Timesteps Collected: 100,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,170.81437
Policy Entropy: 4.01766
Value Function Loss: 0.21529

Mean KL Divergence: 0.00398
SB3 Clip Fraction: 0.17639
Policy Update Magnitude: 0.27820
Value Function Update Magnitude: 0.40932

Collected Steps per Second: 11,422.02133
Overall Steps per Second: 6,719.56550

Timestep Collection Time: 8.75729
Timestep Consumption Time: 6.12849
PPO Batch Consumption Time: 0.47492
Total Iteration Time: 14.88578

Cumulative Model Updates: 10,881
Cumulative Timesteps: 60,994,546

Timesteps Collected: 100,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,598.14627
Policy Entropy: 4.01247
Value Function Loss: 0.19691

Mean KL Divergence: 0.00882
SB3 Clip Fraction: 0.31244
Policy Update Magnitude: 0.24880
Value Function Update Magnitude: 0.36644

Collected Steps per Second: 11,732.95672
Overall Steps per Second: 6,856.45551

Timestep Collection Time: 8.52658
Timestep Consumption Time: 6.06434
PPO Batch Consumption Time: 0.47179
Total Iteration Time: 14.59092

Cumulative Model Updates: 10,890
Cumulative Timesteps: 61,094,588

Timesteps Collected: 100,042
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,748.89148
Policy Entropy: 4.01950
Value Function Loss: 0.18505

Mean KL Divergence: 0.00575
SB3 Clip Fraction: 0.25347
Policy Update Magnitude: 0.24999
Value Function Update Magnitude: 0.34635

Collected Steps per Second: 11,268.57530
Overall Steps per Second: 6,690.55479

Timestep Collection Time: 8.87779
Timestep Consumption Time: 6.07464
PPO Batch Consumption Time: 0.47213
Total Iteration Time: 14.95242

Cumulative Model Updates: 10,899
Cumulative Timesteps: 61,194,628

Timesteps Collected: 100,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,870.44732
Policy Entropy: 4.02173
Value Function Loss: 0.18941

Mean KL Divergence: 0.00381
SB3 Clip Fraction: 0.18553
Policy Update Magnitude: 0.25424
Value Function Update Magnitude: 0.43020

Collected Steps per Second: 11,748.11859
Overall Steps per Second: 6,841.27389

Timestep Collection Time: 8.51268
Timestep Consumption Time: 6.10565
PPO Batch Consumption Time: 0.47448
Total Iteration Time: 14.61833

Cumulative Model Updates: 10,908
Cumulative Timesteps: 61,294,636

Timesteps Collected: 100,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,754.01353
Policy Entropy: 4.02398
Value Function Loss: 0.19017

Mean KL Divergence: 0.00319
SB3 Clip Fraction: 0.15870
Policy Update Magnitude: 0.24966
Value Function Update Magnitude: 0.37736

Collected Steps per Second: 11,503.42689
Overall Steps per Second: 6,742.70491

Timestep Collection Time: 8.69810
Timestep Consumption Time: 6.14134
PPO Batch Consumption Time: 0.47198
Total Iteration Time: 14.83945

Cumulative Model Updates: 10,917
Cumulative Timesteps: 61,394,694

Timesteps Collected: 100,058
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,755.66487
Policy Entropy: 4.02063
Value Function Loss: 0.18869

Mean KL Divergence: 0.00194
SB3 Clip Fraction: 0.09426
Policy Update Magnitude: 0.25017
Value Function Update Magnitude: 0.38071

Collected Steps per Second: 11,639.60246
Overall Steps per Second: 6,802.26253

Timestep Collection Time: 8.59256
Timestep Consumption Time: 6.11049
PPO Batch Consumption Time: 0.47482
Total Iteration Time: 14.70305

Cumulative Model Updates: 10,926
Cumulative Timesteps: 61,494,708

Timesteps Collected: 100,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,252.71431
Policy Entropy: 4.02008
Value Function Loss: 0.19559

Mean KL Divergence: 0.00196
SB3 Clip Fraction: 0.10359
Policy Update Magnitude: 0.24927
Value Function Update Magnitude: 0.39827

Collected Steps per Second: 11,155.85273
Overall Steps per Second: 6,595.82724

Timestep Collection Time: 8.96749
Timestep Consumption Time: 6.19967
PPO Batch Consumption Time: 0.48155
Total Iteration Time: 15.16716

Cumulative Model Updates: 10,935
Cumulative Timesteps: 61,594,748

Timesteps Collected: 100,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,122.70534
Policy Entropy: 4.01559
Value Function Loss: 0.20417

Mean KL Divergence: 0.00182
SB3 Clip Fraction: 0.09142
Policy Update Magnitude: 0.25358
Value Function Update Magnitude: 0.38543

Collected Steps per Second: 11,760.69022
Overall Steps per Second: 6,845.13634

Timestep Collection Time: 8.50375
Timestep Consumption Time: 6.10662
PPO Batch Consumption Time: 0.47497
Total Iteration Time: 14.61037

Cumulative Model Updates: 10,944
Cumulative Timesteps: 61,694,758

Timesteps Collected: 100,010
--------END ITERATION REPORT--------


Saving checkpoint 61694758...
Checkpoint 61694758 saved!
