Created new wandb run! 3gvu0csb
<PERSON>rner successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: -3.77879
Policy Entropy: 4.49941
Value Function Loss: nan

Mean KL Divergence: 0.00020
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.96811
Value Function Update Magnitude: 0.96132

Collected Steps per Second: 9,260.77542
Overall Steps per Second: 2,664.74768

Timestep Collection Time: 10.79845
Timestep Consumption Time: 26.72931
PPO Batch Consumption Time: 6.23807
Total Iteration Time: 37.52776

Cumulative Model Updates: 3
Cumulative Timesteps: 100,002

Timesteps Collected: 100,002
--------END ITERATION REPORT--------
