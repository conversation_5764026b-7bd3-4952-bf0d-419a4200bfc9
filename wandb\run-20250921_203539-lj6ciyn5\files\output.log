Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 367.10205
Policy Entropy: 4.13658
Value Function Loss: 0.04488

Mean KL Divergence: 0.00035
SB3 Clip Fraction: 0.01131
Policy Update Magnitude: 0.07545
Value Function Update Magnitude: 0.16463

Collected Steps per Second: 11,870.98906
Overall Steps per Second: 6,095.77080

Timestep Collection Time: 8.42710
Timestep Consumption Time: 7.98395
PPO Batch Consumption Time: 1.50079
Total Iteration Time: 16.41105

Cumulative Model Updates: 24,456
Cumulative Timesteps: 273,899,042

Timesteps Collected: 100,038
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 376.38690
Policy Entropy: 4.13776
Value Function Loss: 0.04157

Mean KL Divergence: 0.00091
SB3 Clip Fraction: 0.03632
Policy Update Magnitude: 0.16094
Value Function Update Magnitude: 0.35473

Collected Steps per Second: 12,605.44215
Overall Steps per Second: 8,068.89534

Timestep Collection Time: 7.93435
Timestep Consumption Time: 4.46090
PPO Batch Consumption Time: 0.47444
Total Iteration Time: 12.39525

Cumulative Model Updates: 24,462
Cumulative Timesteps: 273,999,058

Timesteps Collected: 100,016
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 682.17291
Policy Entropy: 4.13972
Value Function Loss: 0.04026

Mean KL Divergence: 0.00140
SB3 Clip Fraction: 0.06309
Policy Update Magnitude: 0.22661
Value Function Update Magnitude: 0.49249

Collected Steps per Second: 12,087.97707
Overall Steps per Second: 7,022.82394

Timestep Collection Time: 8.27732
Timestep Consumption Time: 5.96994
PPO Batch Consumption Time: 0.47019
Total Iteration Time: 14.24726

Cumulative Model Updates: 24,471
Cumulative Timesteps: 274,099,114

Timesteps Collected: 100,056
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 585.32849
Policy Entropy: 4.14342
Value Function Loss: 0.03790

Mean KL Divergence: 0.00130
SB3 Clip Fraction: 0.05798
Policy Update Magnitude: 0.20946
Value Function Update Magnitude: 0.46013

Collected Steps per Second: 12,385.62014
Overall Steps per Second: 7,168.01822

Timestep Collection Time: 8.07485
Timestep Consumption Time: 5.87768
PPO Batch Consumption Time: 0.48299
Total Iteration Time: 13.95253

Cumulative Model Updates: 24,480
Cumulative Timesteps: 274,199,126

Timesteps Collected: 100,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 479.08795
Policy Entropy: 4.14482
Value Function Loss: 0.03773

Mean KL Divergence: 0.00131
SB3 Clip Fraction: 0.05748
Policy Update Magnitude: 0.20513
Value Function Update Magnitude: 0.43588

Collected Steps per Second: 10,502.30428
Overall Steps per Second: 6,515.96825

Timestep Collection Time: 9.52705
Timestep Consumption Time: 5.82846
PPO Batch Consumption Time: 0.47384
Total Iteration Time: 15.35551

Cumulative Model Updates: 24,489
Cumulative Timesteps: 274,299,182

Timesteps Collected: 100,056
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 383.46149
Policy Entropy: 4.14700
Value Function Loss: 0.03671

Mean KL Divergence: 0.00135
SB3 Clip Fraction: 0.05993
Policy Update Magnitude: 0.19991
Value Function Update Magnitude: 0.43527

Collected Steps per Second: 11,608.33915
Overall Steps per Second: 6,884.87188

Timestep Collection Time: 8.61725
Timestep Consumption Time: 5.91199
PPO Batch Consumption Time: 0.47652
Total Iteration Time: 14.52925

Cumulative Model Updates: 24,498
Cumulative Timesteps: 274,399,214

Timesteps Collected: 100,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 506.13910
Policy Entropy: 4.14890
Value Function Loss: 0.03756

Mean KL Divergence: 0.00118
SB3 Clip Fraction: 0.05065
Policy Update Magnitude: 0.20375
Value Function Update Magnitude: 0.45028

Collected Steps per Second: 12,199.43722
Overall Steps per Second: 7,167.56437

Timestep Collection Time: 8.20202
Timestep Consumption Time: 5.75809
PPO Batch Consumption Time: 0.47313
Total Iteration Time: 13.96011

Cumulative Model Updates: 24,507
Cumulative Timesteps: 274,499,274

Timesteps Collected: 100,060
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 807.85516
Policy Entropy: 4.14969
Value Function Loss: 0.03733

Mean KL Divergence: 0.00119
SB3 Clip Fraction: 0.05151
Policy Update Magnitude: 0.20390
Value Function Update Magnitude: 0.45531

Collected Steps per Second: 11,681.36699
Overall Steps per Second: 6,940.71774

Timestep Collection Time: 8.56835
Timestep Consumption Time: 5.85235
PPO Batch Consumption Time: 0.47716
Total Iteration Time: 14.42070

Cumulative Model Updates: 24,516
Cumulative Timesteps: 274,599,364

Timesteps Collected: 100,090
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 534.35107
Policy Entropy: 4.14828
Value Function Loss: 0.03762

Mean KL Divergence: 0.00125
SB3 Clip Fraction: 0.05555
Policy Update Magnitude: 0.20296
Value Function Update Magnitude: 0.44691

Collected Steps per Second: 10,382.34377
Overall Steps per Second: 6,405.22175

Timestep Collection Time: 9.63867
Timestep Consumption Time: 5.98483
PPO Batch Consumption Time: 0.48103
Total Iteration Time: 15.62350

Cumulative Model Updates: 24,525
Cumulative Timesteps: 274,699,436

Timesteps Collected: 100,072
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 482.13971
Policy Entropy: 4.14516
Value Function Loss: 0.03803

Mean KL Divergence: 0.00135
SB3 Clip Fraction: 0.05944
Policy Update Magnitude: 0.20200
Value Function Update Magnitude: 0.45157

Collected Steps per Second: 12,488.23953
Overall Steps per Second: 7,154.39006

Timestep Collection Time: 8.01010
Timestep Consumption Time: 5.97181
PPO Batch Consumption Time: 0.47320
Total Iteration Time: 13.98190

Cumulative Model Updates: 24,534
Cumulative Timesteps: 274,799,468

Timesteps Collected: 100,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 588.56268
Policy Entropy: 4.14488
Value Function Loss: 0.03873

Mean KL Divergence: 0.00126
SB3 Clip Fraction: 0.05484
Policy Update Magnitude: 0.20609
Value Function Update Magnitude: 0.44841

Collected Steps per Second: 11,934.40550
Overall Steps per Second: 6,989.76141

Timestep Collection Time: 8.38433
Timestep Consumption Time: 5.93118
PPO Batch Consumption Time: 0.47673
Total Iteration Time: 14.31551

Cumulative Model Updates: 24,543
Cumulative Timesteps: 274,899,530

Timesteps Collected: 100,062
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 400.52692
Policy Entropy: 4.14650
Value Function Loss: 0.03888

Mean KL Divergence: 0.00127
SB3 Clip Fraction: 0.05672
Policy Update Magnitude: 0.20668
Value Function Update Magnitude: 0.45337

Collected Steps per Second: 13,257.19386
Overall Steps per Second: 7,556.15371

Timestep Collection Time: 7.54398
Timestep Consumption Time: 5.69185
PPO Batch Consumption Time: 0.46778
Total Iteration Time: 13.23583

Cumulative Model Updates: 24,552
Cumulative Timesteps: 274,999,542

Timesteps Collected: 100,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 928.28113
Policy Entropy: 4.14737
Value Function Loss: 0.03673

Mean KL Divergence: 0.00133
SB3 Clip Fraction: 0.05698
Policy Update Magnitude: 0.20545
Value Function Update Magnitude: 0.44756

Collected Steps per Second: 12,512.81900
Overall Steps per Second: 7,267.77145

Timestep Collection Time: 7.99692
Timestep Consumption Time: 5.77126
PPO Batch Consumption Time: 0.46725
Total Iteration Time: 13.76818

Cumulative Model Updates: 24,561
Cumulative Timesteps: 275,099,606

Timesteps Collected: 100,064
--------END ITERATION REPORT--------
