Created new wandb run! rfhc<PERSON><PERSON><PERSON> successfully initialized!
Starting training with device: privateuseone:0 (type: directml)
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)



LEARNING LOOP ENCOUNTERED AN ERROR
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 225, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 270, in _learn
    ppo_report = self.ppo_learner.learn(self.experience_buffer)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\ppo\ppo_learner.py", line 182, in learn
    ppo_loss.backward()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\_tensor.py", line 521, in backward
    torch.autograd.backward(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\autograd\__init__.py", line 289, in backward
    _engine_run_backward(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\autograd\graph.py", line 769, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: DirectML scatter doesn't allow partially modified dimensions. Please update the dimension so that the indices and input only differ in the provided dimension.

Saving checkpoint 50008...
Checkpoint 50008 saved!
