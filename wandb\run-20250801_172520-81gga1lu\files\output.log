Created new wandb run! <PERSON>gg<PERSON><PERSON><PERSON>
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 301.04347
Policy Entropy: 4.49888
Value Function Loss: nan

Mean KL Divergence: 0.00000
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.09309
Value Function Update Magnitude: 0.08502

Collected Steps per Second: 11,576.99057
Overall Steps per Second: 8,562.63680

Timestep Collection Time: 8.64059
Timestep Consumption Time: 3.04180
PPO Batch Consumption Time: 0.72116
Total Iteration Time: 11.68238

Cumulative Model Updates: 1
Cumulative Timesteps: 100,032

Timesteps Collected: 100,032
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 220.07723
Policy Entropy: 4.49886
Value Function Loss: 473.58846

Mean KL Divergence: 0.00001
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.14017
Value Function Update Magnitude: 0.15940

Collected Steps per Second: 11,831.55780
Overall Steps per Second: 8,687.07602

Timestep Collection Time: 8.45282
Timestep Consumption Time: 3.05969
PPO Batch Consumption Time: 0.48796
Total Iteration Time: 11.51250

Cumulative Model Updates: 3
Cumulative Timesteps: 200,042

Timesteps Collected: 100,010
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 230.88669
Policy Entropy: 4.49882
Value Function Loss: 0.53591

Mean KL Divergence: 0.00001
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.10179
Value Function Update Magnitude: 0.15044

Collected Steps per Second: 11,472.25542
Overall Steps per Second: 9,024.30638

Timestep Collection Time: 8.71773
Timestep Consumption Time: 2.36479
PPO Batch Consumption Time: 0.14655
Total Iteration Time: 11.08251

Cumulative Model Updates: 5
Cumulative Timesteps: 300,054

Timesteps Collected: 100,012
--------END ITERATION REPORT--------


Saving checkpoint 300054...
Checkpoint 300054 saved!
