2025-09-21 20:40:29,610 INFO    MainThread:17228 [wandb_setup.py:_flush():81] Current SDK version is 0.22.0
2025-09-21 20:40:29,610 INFO    MainThread:17228 [wandb_setup.py:_flush():81] Configure stats pid to 17228
2025-09-21 20:40:29,610 INFO    MainThread:17228 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-09-21 20:40:29,610 INFO    MainThread:17228 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\Downloads\Rlgym\Rlgym\wandb\settings
2025-09-21 20:40:29,610 INFO    MainThread:17228 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-09-21 20:40:29,611 INFO    MainThread:17228 [wandb_init.py:setup_run_log_directory():686] Logging user logs to C:\Users\<USER>\Downloads\Rlgym\Rlgym\wandb\run-20250921_204029-7feljtgl\logs\debug.log
2025-09-21 20:40:29,611 INFO    MainThread:17228 [wandb_init.py:setup_run_log_directory():687] Logging internal logs to C:\Users\<USER>\Downloads\Rlgym\Rlgym\wandb\run-20250921_204029-7feljtgl\logs\debug-internal.log
2025-09-21 20:40:29,611 INFO    MainThread:17228 [wandb_init.py:init():813] calling init triggers
2025-09-21 20:40:29,611 INFO    MainThread:17228 [wandb_init.py:init():818] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 100000000000, 'exp_buffer_size': 300000, 'ts_per_iteration': 100000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (512, 512, 512, 512), 'critic_layer_sizes': (512, 512, 512, 512), 'ppo_epochs': 1, 'ppo_batch_size': 100000, 'ppo_minibatch_size': 50000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-09-21 20:40:29,611 INFO    MainThread:17228 [wandb_init.py:init():861] starting backend
2025-09-21 20:40:31,932 INFO    MainThread:17228 [wandb_init.py:init():864] sending inform_init request
2025-09-21 20:40:31,942 INFO    MainThread:17228 [wandb_init.py:init():872] backend started and connected
2025-09-21 20:40:31,943 INFO    MainThread:17228 [wandb_init.py:init():942] updated telemetry
2025-09-21 20:40:31,945 INFO    MainThread:17228 [wandb_init.py:init():966] communicating run to backend with 90.0 second timeout
2025-09-21 20:40:32,441 INFO    MainThread:17228 [wandb_init.py:init():1017] starting run threads in backend
2025-09-21 20:40:32,569 INFO    MainThread:17228 [wandb_run.py:_console_start():2506] atexit reg
2025-09-21 20:40:32,570 INFO    MainThread:17228 [wandb_run.py:_redirect():2354] redirect: wrap_raw
2025-09-21 20:40:32,570 INFO    MainThread:17228 [wandb_run.py:_redirect():2423] Wrapping output streams.
2025-09-21 20:40:32,570 INFO    MainThread:17228 [wandb_run.py:_redirect():2446] Redirects installed.
2025-09-21 20:40:32,573 INFO    MainThread:17228 [wandb_init.py:init():1057] run started, returning control to user process
2025-09-21 20:44:26,917 INFO    MainThread:17228 [wandb_run.py:_finish():2272] finishing run xzskullyxz-rlgym/rlgym-ppo/7feljtgl
2025-09-21 20:44:26,918 INFO    MainThread:17228 [wandb_run.py:_atexit_cleanup():2471] got exitcode: 0
2025-09-21 20:44:26,918 INFO    MainThread:17228 [wandb_run.py:_restore():2453] restore
2025-09-21 20:44:26,918 INFO    MainThread:17228 [wandb_run.py:_restore():2459] restore done
2025-09-21 20:44:27,513 INFO    MainThread:17228 [wandb_run.py:_footer_sync_info():3867] logging synced files
