Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,213.37436
Policy Entropy: 4.03326
Value Function Loss: 0.03333

Mean KL Divergence: 0.00063
SB3 Clip Fraction: 0.02664
Policy Update Magnitude: 0.09224
Value Function Update Magnitude: 0.15775

Collected Steps per Second: 9,729.53598
Overall Steps per Second: 5,891.69418

Timestep Collection Time: 5.14064
Timestep Consumption Time: 3.34860
PPO Batch Consumption Time: 0.66623
Total Iteration Time: 8.48924

Cumulative Model Updates: 20,562
Cumulative Timesteps: 114,782,166

Timesteps Collected: 50,016
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,884.20458
Policy Entropy: 4.02960
Value Function Loss: 0.02948

Mean KL Divergence: 0.00083
SB3 Clip Fraction: 0.03161
Policy Update Magnitude: 0.10374
Value Function Update Magnitude: 0.17421

Collected Steps per Second: 10,710.56500
Overall Steps per Second: 7,936.24519

Timestep Collection Time: 4.66885
Timestep Consumption Time: 1.63212
PPO Batch Consumption Time: 0.24673
Total Iteration Time: 6.30096

Cumulative Model Updates: 20,565
Cumulative Timesteps: 114,832,172

Timesteps Collected: 50,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 1,830.25376
Policy Entropy: 4.03246
Value Function Loss: 0.02931

Mean KL Divergence: 0.00128
SB3 Clip Fraction: 0.05711
Policy Update Magnitude: 0.19511
Value Function Update Magnitude: 0.32056

Collected Steps per Second: 10,691.24917
Overall Steps per Second: 7,065.09591

Timestep Collection Time: 4.67841
Timestep Consumption Time: 2.40119
PPO Batch Consumption Time: 0.24647
Total Iteration Time: 7.07959

Cumulative Model Updates: 20,571
Cumulative Timesteps: 114,882,190

Timesteps Collected: 50,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,479.06862
Policy Entropy: 4.02952
Value Function Loss: 0.02590

Mean KL Divergence: 0.00136
SB3 Clip Fraction: 0.05842
Policy Update Magnitude: 0.26148
Value Function Update Magnitude: 0.44063

Collected Steps per Second: 10,661.34330
Overall Steps per Second: 6,401.49274

Timestep Collection Time: 4.69097
Timestep Consumption Time: 3.12159
PPO Batch Consumption Time: 0.24559
Total Iteration Time: 7.81255

Cumulative Model Updates: 20,580
Cumulative Timesteps: 114,932,202

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,713.89647
Policy Entropy: 4.02742
Value Function Loss: 0.02426

Mean KL Divergence: 0.00157
SB3 Clip Fraction: 0.07268
Policy Update Magnitude: 0.24912
Value Function Update Magnitude: 0.39367

Collected Steps per Second: 10,742.44016
Overall Steps per Second: 6,429.09464

Timestep Collection Time: 4.65593
Timestep Consumption Time: 3.12371
PPO Batch Consumption Time: 0.24530
Total Iteration Time: 7.77963

Cumulative Model Updates: 20,589
Cumulative Timesteps: 114,982,218

Timesteps Collected: 50,016
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,721.65599
Policy Entropy: 4.03093
Value Function Loss: 0.02470

Mean KL Divergence: 0.00133
SB3 Clip Fraction: 0.05837
Policy Update Magnitude: 0.24090
Value Function Update Magnitude: 0.37551

Collected Steps per Second: 10,733.87038
Overall Steps per Second: 6,431.44134

Timestep Collection Time: 4.65871
Timestep Consumption Time: 3.11653
PPO Batch Consumption Time: 0.24572
Total Iteration Time: 7.77524

Cumulative Model Updates: 20,598
Cumulative Timesteps: 115,032,224

Timesteps Collected: 50,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 6,521.87665
Policy Entropy: 4.03005
Value Function Loss: 0.02495

Mean KL Divergence: 0.00120
SB3 Clip Fraction: 0.05215
Policy Update Magnitude: 0.24503
Value Function Update Magnitude: 0.36607

Collected Steps per Second: 10,797.19110
Overall Steps per Second: 6,426.92016

Timestep Collection Time: 4.63120
Timestep Consumption Time: 3.14919
PPO Batch Consumption Time: 0.24627
Total Iteration Time: 7.78040

Cumulative Model Updates: 20,607
Cumulative Timesteps: 115,082,228

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,090.44950
Policy Entropy: 4.03162
Value Function Loss: 0.02788

Mean KL Divergence: 0.00121
SB3 Clip Fraction: 0.05160
Policy Update Magnitude: 0.25655
Value Function Update Magnitude: 0.40210

Collected Steps per Second: 9,918.57942
Overall Steps per Second: 6,124.84238

Timestep Collection Time: 5.04165
Timestep Consumption Time: 3.12281
PPO Batch Consumption Time: 0.24377
Total Iteration Time: 8.16446

Cumulative Model Updates: 20,616
Cumulative Timesteps: 115,132,234

Timesteps Collected: 50,006
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,817.03823
Policy Entropy: 4.03155
Value Function Loss: 0.02876

Mean KL Divergence: 0.00147
SB3 Clip Fraction: 0.06744
Policy Update Magnitude: 0.26500
Value Function Update Magnitude: 0.43610

Collected Steps per Second: 11,479.40407
Overall Steps per Second: 6,682.76519

Timestep Collection Time: 4.35632
Timestep Consumption Time: 3.12681
PPO Batch Consumption Time: 0.24675
Total Iteration Time: 7.48313

Cumulative Model Updates: 20,625
Cumulative Timesteps: 115,182,242

Timesteps Collected: 50,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,521.51497
Policy Entropy: 4.03108
Value Function Loss: 0.02737

Mean KL Divergence: 0.00186
SB3 Clip Fraction: 0.08821
Policy Update Magnitude: 0.26463
Value Function Update Magnitude: 0.45312

Collected Steps per Second: 10,037.37266
Overall Steps per Second: 6,091.77885

Timestep Collection Time: 4.98158
Timestep Consumption Time: 3.22653
PPO Batch Consumption Time: 0.24718
Total Iteration Time: 8.20811

Cumulative Model Updates: 20,634
Cumulative Timesteps: 115,232,244

Timesteps Collected: 50,002
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,503.27492
Policy Entropy: 4.03013
Value Function Loss: 0.02724

Mean KL Divergence: 0.00174
SB3 Clip Fraction: 0.08225
Policy Update Magnitude: 0.25627
Value Function Update Magnitude: 0.44595

Collected Steps per Second: 8,587.95801
Overall Steps per Second: 5,476.42021

Timestep Collection Time: 5.82630
Timestep Consumption Time: 3.31033
PPO Batch Consumption Time: 0.25021
Total Iteration Time: 9.13663

Cumulative Model Updates: 20,643
Cumulative Timesteps: 115,282,280

Timesteps Collected: 50,036
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,015.48318
Policy Entropy: 4.02667
Value Function Loss: 0.02697

Mean KL Divergence: 0.00139
SB3 Clip Fraction: 0.06082
Policy Update Magnitude: 0.25527
Value Function Update Magnitude: 0.42287

Collected Steps per Second: 10,165.54905
Overall Steps per Second: 6,225.79242

Timestep Collection Time: 4.91956
Timestep Consumption Time: 3.11316
PPO Batch Consumption Time: 0.24425
Total Iteration Time: 8.03271

Cumulative Model Updates: 20,652
Cumulative Timesteps: 115,332,290

Timesteps Collected: 50,010
--------END ITERATION REPORT--------


Saving checkpoint 115332290...
Checkpoint 115332290 saved!
