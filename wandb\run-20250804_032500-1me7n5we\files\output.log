Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 1,286.81356
Policy Entropy: 3.89320
Value Function Loss: 0.28854

Mean KL Divergence: 0.00071
SB3 Clip Fraction: 0.03100
Policy Update Magnitude: 0.13809
Value Function Update Magnitude: 0.21127

Collected Steps per Second: 12,806.34766
Overall Steps per Second: 6,740.35637

Timestep Collection Time: 3.91119
Timestep Consumption Time: 3.51988
PPO Batch Consumption Time: 0.68502
Total Iteration Time: 7.43106

Cumulative Model Updates: 10,245
Cumulative Timesteps: 57,143,032

Timesteps Collected: 50,088
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,267.49346
Policy Entropy: 3.91114
Value Function Loss: 0.22699

Mean KL Divergence: 0.00418
SB3 Clip Fraction: 0.18797
Policy Update Magnitude: 0.29962
Value Function Update Magnitude: 0.55904

Collected Steps per Second: 14,330.79557
Overall Steps per Second: 8,517.79159

Timestep Collection Time: 3.49220
Timestep Consumption Time: 2.38327
PPO Batch Consumption Time: 0.24600
Total Iteration Time: 5.87547

Cumulative Model Updates: 10,251
Cumulative Timesteps: 57,193,078

Timesteps Collected: 50,046
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,300.03197
Policy Entropy: 3.91990
Value Function Loss: 0.19268

Mean KL Divergence: 0.00579
SB3 Clip Fraction: 0.24528
Policy Update Magnitude: 0.38547
Value Function Update Magnitude: 0.82289

Collected Steps per Second: 12,885.62789
Overall Steps per Second: 7,099.67520

Timestep Collection Time: 3.88572
Timestep Consumption Time: 3.16671
PPO Batch Consumption Time: 0.25252
Total Iteration Time: 7.05244

Cumulative Model Updates: 10,260
Cumulative Timesteps: 57,243,148

Timesteps Collected: 50,070
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,486.71206
Policy Entropy: 3.94233
Value Function Loss: 0.16327

Mean KL Divergence: 0.00510
SB3 Clip Fraction: 0.22872
Policy Update Magnitude: 0.34017
Value Function Update Magnitude: 0.72245

Collected Steps per Second: 12,494.25335
Overall Steps per Second: 6,984.29345

Timestep Collection Time: 4.00472
Timestep Consumption Time: 3.15935
PPO Batch Consumption Time: 0.25446
Total Iteration Time: 7.16407

Cumulative Model Updates: 10,269
Cumulative Timesteps: 57,293,184

Timesteps Collected: 50,036
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,779.55709
Policy Entropy: 3.94486
Value Function Loss: 0.15380

Mean KL Divergence: 0.00432
SB3 Clip Fraction: 0.20054
Policy Update Magnitude: 0.32364
Value Function Update Magnitude: 0.76623

Collected Steps per Second: 13,526.65015
Overall Steps per Second: 7,436.26818

Timestep Collection Time: 3.70040
Timestep Consumption Time: 3.03067
PPO Batch Consumption Time: 0.24446
Total Iteration Time: 6.73106

Cumulative Model Updates: 10,278
Cumulative Timesteps: 57,343,238

Timesteps Collected: 50,054
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 3,764.79717
Policy Entropy: 3.94789
Value Function Loss: 0.14513

Mean KL Divergence: 0.00313
SB3 Clip Fraction: 0.15375
Policy Update Magnitude: 0.32453
Value Function Update Magnitude: 0.65718

Collected Steps per Second: 14,380.15362
Overall Steps per Second: 7,671.71658

Timestep Collection Time: 3.48286
Timestep Consumption Time: 3.04554
PPO Batch Consumption Time: 0.24660
Total Iteration Time: 6.52840

Cumulative Model Updates: 10,287
Cumulative Timesteps: 57,393,322

Timesteps Collected: 50,084
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,493.22934
Policy Entropy: 3.94885
Value Function Loss: 0.13932

Mean KL Divergence: 0.00241
SB3 Clip Fraction: 0.12405
Policy Update Magnitude: 0.32045
Value Function Update Magnitude: 0.46655

Collected Steps per Second: 11,561.32889
Overall Steps per Second: 6,560.70806

Timestep Collection Time: 4.32580
Timestep Consumption Time: 3.29716
PPO Batch Consumption Time: 0.25351
Total Iteration Time: 7.62296

Cumulative Model Updates: 10,296
Cumulative Timesteps: 57,443,334

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,736.32116
Policy Entropy: 3.95088
Value Function Loss: 0.14582

Mean KL Divergence: 0.00234
SB3 Clip Fraction: 0.12098
Policy Update Magnitude: 0.32007
Value Function Update Magnitude: 0.47799

Collected Steps per Second: 12,496.19239
Overall Steps per Second: 6,352.56325

Timestep Collection Time: 4.00218
Timestep Consumption Time: 3.87055
PPO Batch Consumption Time: 0.32770
Total Iteration Time: 7.87273

Cumulative Model Updates: 10,305
Cumulative Timesteps: 57,493,346

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 4,846.60187
Policy Entropy: 3.95266
Value Function Loss: 0.14110

Mean KL Divergence: 0.00207
SB3 Clip Fraction: 0.10673
Policy Update Magnitude: 0.31521
Value Function Update Magnitude: 0.42699

Collected Steps per Second: 7,758.18560
Overall Steps per Second: 5,145.87834

Timestep Collection Time: 6.45331
Timestep Consumption Time: 3.27603
PPO Batch Consumption Time: 0.25092
Total Iteration Time: 9.72934

Cumulative Model Updates: 10,314
Cumulative Timesteps: 57,543,412

Timesteps Collected: 50,066
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,745.00856
Policy Entropy: 3.94756
Value Function Loss: 0.16437

Mean KL Divergence: 0.00240
SB3 Clip Fraction: 0.12167
Policy Update Magnitude: 0.31711
Value Function Update Magnitude: 0.39913

Collected Steps per Second: 14,149.09498
Overall Steps per Second: 7,591.60440

Timestep Collection Time: 3.53592
Timestep Consumption Time: 3.05426
PPO Batch Consumption Time: 0.24450
Total Iteration Time: 6.59017

Cumulative Model Updates: 10,323
Cumulative Timesteps: 57,593,442

Timesteps Collected: 50,030
--------END ITERATION REPORT--------


Saving checkpoint 57593442...
Checkpoint 57593442 saved!
