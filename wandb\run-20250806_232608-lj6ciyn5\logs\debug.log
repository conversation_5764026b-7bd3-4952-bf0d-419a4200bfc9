2025-08-06 23:26:08,075 INFO    MainThread:9252 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-06 23:26:08,075 INFO    MainThread:9252 [wandb_setup.py:_flush():80] Configure stats pid to 9252
2025-08-06 23:26:08,075 INFO    MainThread:9252 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-08-06 23:26:08,075 INFO    MainThread:9252 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-08-06 23:26:08,075 INFO    MainThread:9252 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-06 23:26:08,077 INFO    MainThread:9252 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250806_232608-lj6ciyn5\logs\debug.log
2025-08-06 23:26:08,077 INFO    MainThread:9252 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250806_232608-lj6ciyn5\logs\debug-internal.log
2025-08-06 23:26:08,077 INFO    MainThread:9252 [wandb_init.py:init():830] calling init triggers
2025-08-06 23:26:08,077 INFO    MainThread:9252 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 64, 'min_inference_size': 58, 'timestep_limit': 100000000000, 'exp_buffer_size': 300000, 'ts_per_iteration': 100000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': [1024, 1024, 512, 512], 'critic_layer_sizes': [1024, 1024, 512, 512], 'ppo_epochs': 3, 'ppo_batch_size': 100000, 'ppo_minibatch_size': 50000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-08-06 23:26:08,077 INFO    MainThread:9252 [wandb_init.py:init():871] starting backend
2025-08-06 23:26:08,409 INFO    MainThread:9252 [wandb_init.py:init():874] sending inform_init request
2025-08-06 23:26:08,422 INFO    MainThread:9252 [wandb_init.py:init():882] backend started and connected
2025-08-06 23:26:08,424 INFO    MainThread:9252 [wandb_init.py:init():953] updated telemetry
2025-08-06 23:26:08,426 INFO    MainThread:9252 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-06 23:26:09,138 INFO    MainThread:9252 [wandb_init.py:init():1024] run resumed
2025-08-06 23:26:09,139 INFO    MainThread:9252 [wandb_init.py:init():1029] starting run threads in backend
2025-08-06 23:26:10,291 INFO    MainThread:9252 [wandb_run.py:_console_start():2458] atexit reg
2025-08-06 23:26:10,291 INFO    MainThread:9252 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-06 23:26:10,293 INFO    MainThread:9252 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-06 23:26:10,293 INFO    MainThread:9252 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-06 23:26:10,295 INFO    MainThread:9252 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-06 23:32:36,428 INFO    MainThread:9252 [wandb_run.py:_finish():2224] finishing run xzskullyxz-rlgym/rlgym-ppo/lj6ciyn5
2025-08-06 23:32:36,428 INFO    MainThread:9252 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-08-06 23:32:36,428 INFO    MainThread:9252 [wandb_run.py:_restore():2405] restore
2025-08-06 23:32:36,428 INFO    MainThread:9252 [wandb_run.py:_restore():2411] restore done
2025-08-06 23:32:37,125 INFO    MainThread:9252 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-08-06 23:32:37,126 INFO    MainThread:9252 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-08-06 23:32:37,126 INFO    MainThread:9252 [wandb_run.py:_footer_sync_info():3864] logging synced files
