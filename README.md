# RLGymPPO with DirectML Support

This is an enhanced version of RLGymPPO that includes DirectML support for GPU acceleration on Windows systems. DirectML enables GPU acceleration for AMD, Intel, and NVIDIA GPUs through Microsoft's DirectX Machine Learning library.

## Features

- **DirectML Integration**: GPU acceleration on Windows for all DirectX 12-compatible GPUs
- **Automatic Device Detection**: Intelligent fallback from DirectML → CUDA → CPU
- **Cross-GPU Compatibility**: Works with AMD, Intel, and NVIDIA GPUs on Windows
- **Backward Compatibility**: Fully compatible with existing RLGymPPO code

## INSTALLATION

### Basic Installation
This repo made by zealan will cover everything from installing dependencies to creating and making your own bot: [Zealan Guide](https://github.com/ZealanL/RLGym-PPO-Guide/blob/main/intro.md).

### DirectML Installation (Windows GPU Acceleration)

#### Requirements
- Windows 10 version 1709 (Build 16299) or higher
- Updated GPU drivers
- DirectX 12-compatible GPU (AMD GCN 1st Gen+, Intel Haswell+, NVIDIA Kepler+)

#### Installation Steps
1. Install the base requirements:
   ```bash
   pip install -r requirements.txt
   ```

2. Install DirectML support:
   ```bash
   pip install torch-directml
   ```

3. Verify installation:
   ```python
   python example_directml.py
   ```

## Usage

### Device Selection Options

The enhanced RLGymPPO supports multiple device options:

- `"auto"` - Automatically select best available device (DirectML → CUDA → CPU)
- `"directml"` or `"dml"` - Force DirectML usage (Windows GPU acceleration)
- `"cuda"` - Force CUDA usage (NVIDIA GPU acceleration)
- `"cpu"` - Force CPU computation
- `"gpu"` - Try any GPU acceleration, fallback to CPU

### Example Usage

#### Basic Usage with Automatic Device Selection
```python
from rlgym_ppo import Learner

learner = Learner(
    env_create_function,
    device="auto",  # Automatically selects best device
    # ... other parameters
)
learner.learn()
```

#### DirectML-Specific Example
```python
# Run the DirectML example
python example_directml.py
```

#### Manual Device Selection
```python
from rlgym_ppo import Learner

learner = Learner(
    env_create_function,
    device="directml",  # Force DirectML usage
    # ... other parameters
)
learner.learn()
```

### Device Information
You can check available devices:

```python
from rlgym_ppo.util import get_device_manager

device_manager = get_device_manager()
device_manager.log_device_info()
info = device_manager.get_device_info()
print(info)
```

## Performance Benefits

DirectML can provide significant performance improvements on Windows systems:

- **AMD GPUs**: Native GPU acceleration without ROCm complexity
- **Intel GPUs**: Leverage integrated and discrete Intel graphics
- **NVIDIA GPUs**: Alternative to CUDA with broader Windows compatibility
- **Unified API**: Single codebase works across all GPU vendors

## Troubleshooting

### DirectML Not Available
If DirectML is not available, the system will automatically fall back to CUDA or CPU:

```
WARNING: torch-directml package not installed. DirectML support disabled.
INFO: To install DirectML support: pip install torch-directml
```

### Installation Issues
1. Ensure Windows version compatibility (Windows 10 1709+)
2. Update GPU drivers through Windows Update
3. Verify DirectX 12 support: `dxdiag`

### Performance Issues
- DirectML performance may vary by GPU generation
- For NVIDIA GPUs, CUDA might be faster than DirectML
- Use `device="auto"` to let the system choose the optimal backend

## Visualizer
The example.py already has code implemented to use Zealan's 'RocketSimVis' so you will be able to see how your bot plays in a virtually created environment while it trains. This is not needed but if you want to see your bot play you can download it here: [RocketSimVis](https://github.com/ZealanL/RocketSimVis).

## Examples

- `example.py` - Standard example with automatic device selection
- `example_directml.py` - DirectML-specific example with device checking

## Contributing

When contributing, please ensure DirectML compatibility is maintained and test on multiple device types when possible.
